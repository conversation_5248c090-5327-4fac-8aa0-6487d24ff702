package com.yixun.wid.v2.vo;

import com.yixun.wid.entity.Cases;
import com.yixun.wid.entity.Declaration;
import lombok.Data;

/**
 * AI材料审核内部请求参数（用于AiUtils调用）
 */
@Data
public class MaterialAuditInternalRequestVO {

    /**
     * 类型：declaration 或 cases
     */
    private String type;

    /**
     * 申报记录（当type为declaration时使用）
     */
    private Declaration declaration;

    /**
     * 案件记录（当type为cases时使用）
     */
    private Cases cases;

    /**
     * 构造方法 - Declaration类型
     */
    public static MaterialAuditInternalRequestVO forDeclaration(Declaration declaration) {
        MaterialAuditInternalRequestVO request = new MaterialAuditInternalRequestVO();
        request.setType("declaration");
        request.setDeclaration(declaration);
        return request;
    }

    /**
     * 构造方法 - Cases类型
     */
    public static MaterialAuditInternalRequestVO forCases(Cases cases) {
        MaterialAuditInternalRequestVO request = new MaterialAuditInternalRequestVO();
        request.setType("cases");
        request.setCases(cases);
        return request;
    }
}
