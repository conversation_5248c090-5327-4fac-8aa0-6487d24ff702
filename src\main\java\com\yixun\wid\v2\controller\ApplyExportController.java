package com.yixun.wid.v2.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.ExcelWriter;
import cn.idev.excel.support.ExcelTypeEnum;
import cn.idev.excel.write.metadata.WriteSheet;
import cn.idev.excel.write.metadata.fill.FillConfig;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import com.yixun.bean.CommonResult;
import com.yixun.wid.entity.Cases;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.DiagnosticInfo;
import com.yixun.wid.entity.User;
import com.yixun.wid.service.CasesService;
import com.yixun.wid.service.DeclarationService;
import com.yixun.wid.service.UserService;
import com.yixun.wid.v2.service.ExportLogService;
import com.yixun.wid.v2.utils.DataMaskUtil;
import com.yixun.wid.v2.utils.ServletRequestUtils;
import com.yixun.wid.v2.vo.ApplyExportRequestVO;
import com.yixun.wid.v2.vo.ApplyRecordExportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Collectors;

/**
 * 申请导出相关接口
 */
@Slf4j
@Api(tags = "申请导出相关接口")
@RequestMapping("/v2/apply/export")
@RestController
@AllArgsConstructor
public class ApplyExportController {

    private final MongoTemplate mongoTemplate;
    private final DeclarationService declarationService;
    private final CasesService casesService;
    private final UserService userService;
    private final ExportLogService exportLogService;

    /**
     * 导出申请记录
     */
    @PostMapping("/records")
    @ApiOperation("导出申请记录")
    public void exportApplyRecords(@Valid @RequestBody ApplyExportRequestVO requestVO) throws UnsupportedEncodingException {
        log.info("开始导出申请记录，参数：{}", requestVO);

        // 根据业务类型决定查询哪张表
        List<ApplyRecordExportVO> exportData;
        if ("工伤事故报备".equals(requestVO.getBusinessType())) {
            // 查询Cases表
            Query query = buildCasesQuery(requestVO);
            log.info("Cases查询条件：{}", query);
            List<Cases> cases = mongoTemplate.find(query, Cases.class);
            log.info("Cases查询结果数量：{}", cases.size());
            exportData = convertCasesToExportData(cases);
        } else {
            // 默认查询Declaration表（工伤认定申请）
            Query query = buildDeclarationQuery(requestVO);
            log.info("Declaration查询条件：{}", query);
            List<Declaration> declarations = mongoTemplate.find(query, Declaration.class);
            log.info("Declaration查询结果数量：{}", declarations.size());
            exportData = convertDeclarationsToExportData(declarations);
        }

        log.info("转换后的导出数据数量：{}", exportData.size());

        // 设置响应头
        HttpServletResponse response = ServletRequestUtils.getResp();
        response.setContentType(ExcelUtil.XLSX_CONTENT_TYPE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.toString());

        String fileName = generateFileName(requestVO, exportData);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" +
            URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));

        // 使用模板导出Excel
        try {
            // 加载模板文件
            ClassPathResource templateResource = new ClassPathResource("file/小程序进度状态批量导出.xlsx");
            InputStream templateInputStream = templateResource.getInputStream();

            // 创建样式策略
            HorizontalCellStyleStrategy styleStrategy = createCellStyleStrategy();

            // 使用模板导出
            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .needHead(false)
                .withTemplate(templateInputStream)
                .registerWriteHandler(styleStrategy)
                .excelType(ExcelTypeEnum.XLSX).build()) {

                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();

                // 填充数据到模板
                excelWriter.fill(exportData, fillConfig, writeSheet);
                excelWriter.finish();
            }

            templateInputStream.close();

            // 记录导出成功日志
            exportLogService.recordApplyExportLog(requestVO, fileName, exportData.size(), "SUCCESS", null);

        } catch (IOException e) {
            log.error("导出申请记录失败", e);

            // 记录导出失败日志
            exportLogService.recordApplyExportLog(requestVO, fileName, exportData.size(), "FAILED", e.getMessage());

            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    /**
     * 构建Declaration查询条件
     */
    private Query buildDeclarationQuery(ApplyExportRequestVO requestVO) {
        Query query = new Query();

        // 用人单位条件（必填）
        query.addCriteria(Criteria.where("organizationId").is(requestVO.getOrganizationId()));

        // 工伤认定申请的状态条件
        query.addCriteria(Criteria.where("status").in(Arrays.asList(
            "Applying", "Accepting", "Identifying", "Done", "Canceling", "Cancelled", "Rejected"
        )));

        // 时间范围条件
        Date[] timeRange = getTimeRange(requestVO.getTimeRange());
        if (timeRange != null && timeRange.length == 2) {
            query.addCriteria(Criteria.where("submitTime").gte(timeRange[0]).lte(timeRange[1]));
        }

        // 按提交时间倒序排列
        query.with(Sort.by(Sort.Direction.DESC, "submitTime"));

        return query;
    }

    /**
     * 构建Cases查询条件
     */
    private Query buildCasesQuery(ApplyExportRequestVO requestVO) {
        Query query = new Query();

        // 用人单位条件（必填）
        query.addCriteria(Criteria.where("organizationId").is(requestVO.getOrganizationId()));

        // 工伤事故报备的状态条件（排除Start状态）
        query.addCriteria(Criteria.where("status").ne("Start"));

        // 时间范围条件
        Date[] timeRange = getTimeRange(requestVO.getTimeRange());
        if (timeRange != null && timeRange.length == 2) {
            query.addCriteria(Criteria.where("submitTime").gte(timeRange[0]).lte(timeRange[1]));
        }

        // 按提交时间倒序排列
        query.with(Sort.by(Sort.Direction.DESC, "submitTime"));

        return query;
    }

    /**
     * 根据时间范围月份数获取时间区间
     */
    private Date[] getTimeRange(Integer timeRangeMonths) {
        if (timeRangeMonths == null) {
            timeRangeMonths = 1;
        }

        // 限制范围在1-12之间
        if (timeRangeMonths < 1) {
            timeRangeMonths = 1;
        } else if (timeRangeMonths > 12) {
            timeRangeMonths = 12;
        }

        Date endTime = new Date();
        Date startTime = DateUtil.offsetMonth(endTime, -timeRangeMonths);

        log.info("时间范围计算：{}个月，开始时间：{}，结束时间：{}",
            timeRangeMonths, DateUtil.formatDateTime(startTime), DateUtil.formatDateTime(endTime));

        return new Date[]{startTime, endTime};
    }

    /**
     * 转换Declaration为导出数据
     */
    private List<ApplyRecordExportVO> convertDeclarationsToExportData(List<Declaration> declarations) {
        List<ApplyRecordExportVO> exportData = new ArrayList<>();

        for (int i = 0; i < declarations.size(); i++) {
            Declaration declaration = declarations.get(i);
            ApplyRecordExportVO exportVO = new ApplyRecordExportVO();

            // 序号
            exportVO.setSerialNumber(i + 1);

            // 用人单位名称
            exportVO.setOrganizationName(declaration.getOrganization());

            // 申请时间
            exportVO.setSubmitTime(declaration.getSubmitTime());

            // 经办人信息（从Declaration实体获取）
            exportVO.setOperatorName(DataMaskUtil.maskName(declaration.getApplicantName()));
            exportVO.setOperatorPhone(DataMaskUtil.maskPhone(declaration.getApplicantPhone()));

            // 与受伤害职工关系
            exportVO.setRelationship(declaration.getRelationship());

            // 受伤害职工信息
            exportVO.setInjuredWorkerName(DataMaskUtil.maskName(declaration.getName()));
            exportVO.setGender(declaration.getGender());
            exportVO.setIdCard(DataMaskUtil.maskIdCard(declaration.getIdCard()));
            exportVO.setInjuredWorkerPhone(DataMaskUtil.maskPhone(declaration.getPhone()));
            exportVO.setFirstWorkDate(declaration.getFirstWorkDate());

            // 事故信息
            exportVO.setAccidentTime(declaration.getAccidentTime());
            exportVO.setAccidentLocation(declaration.getAccidentAddress());
            exportVO.setInjuredPart(convertListToString(declaration.getInjuredPart()));
            exportVO.setAccidentDescription(declaration.getAccidentDetail());

            // 就诊信息
            String medicalInfo = buildDeclarationMedicalInfo(declaration);
            exportVO.setMedicalInfo(medicalInfo);

            // 当前进度状态
            String currentStatus = buildDeclarationCurrentStatus(declaration);
            exportVO.setCurrentStatus(currentStatus);

            exportData.add(exportVO);
        }

        return exportData;
    }

    /**
     * 转换Cases为导出数据
     */
    private List<ApplyRecordExportVO> convertCasesToExportData(List<Cases> cases) {
        List<ApplyRecordExportVO> exportData = new ArrayList<>();

        for (int i = 0; i < cases.size(); i++) {
            Cases casesItem = cases.get(i);
            ApplyRecordExportVO exportVO = new ApplyRecordExportVO();

            // 序号
            exportVO.setSerialNumber(i + 1);

            // 用人单位名称
            exportVO.setOrganizationName(casesItem.getOrganization());

            // 申请时间
            exportVO.setSubmitTime(casesItem.getSubmitTime());

            // 经办人信息（从Cases实体获取）
            exportVO.setOperatorName(DataMaskUtil.maskName(casesItem.getReportName()));
            exportVO.setOperatorPhone(DataMaskUtil.maskPhone(casesItem.getReportPhone()));

            // 与受伤害职工关系
            exportVO.setRelationship(casesItem.getRelationship());

            // 受伤害职工信息
            exportVO.setInjuredWorkerName(DataMaskUtil.maskName(casesItem.getName()));
            exportVO.setGender(casesItem.getGender());
            exportVO.setIdCard(DataMaskUtil.maskIdCard(casesItem.getIdCard()));
            exportVO.setInjuredWorkerPhone(DataMaskUtil.maskPhone(casesItem.getPhone()));
            exportVO.setFirstWorkDate(casesItem.getFirstWorkDate());

            // 事故信息
            exportVO.setAccidentTime(casesItem.getAccidentTime());
            exportVO.setAccidentLocation(casesItem.getAccidentAddress());
            exportVO.setInjuredPart(convertListToString(casesItem.getInjuredPart()));
            exportVO.setAccidentDescription(casesItem.getAccidentDetail());

            // 就诊信息
            String medicalInfo = buildCasesMedicalInfo(casesItem);
            exportVO.setMedicalInfo(medicalInfo);

            // 当前进度状态
            String currentStatus = buildCasesCurrentStatus(casesItem);
            exportVO.setCurrentStatus(currentStatus);

            exportData.add(exportVO);
        }

        return exportData;
    }

    /**
     * 构建Declaration就诊信息
     * 格式：就诊医院1，就诊日期1，诊断结论1，诊断结论2，诊断结论3；\n就诊医院2，就诊日期2，诊断结论1，诊断结论2，诊断结论3；
     */
    private String buildDeclarationMedicalInfo(Declaration declaration) {
        StringBuilder medicalInfo = new StringBuilder();

        // 优先使用diagnosticInfoList（新版诊断信息）
        if (ObjectUtil.isNotEmpty(declaration.getDiagnosticInfoList())) {
            for (int i = 0; i < declaration.getDiagnosticInfoList().size(); i++) {
                if (i > 0) {
                    medicalInfo.append("\n");
                }

                DiagnosticInfo diagnosticInfo = declaration.getDiagnosticInfoList().get(i);

                // 就诊医院
                if (StrUtil.isNotBlank(diagnosticInfo.getHospital())) {
                    medicalInfo.append(diagnosticInfo.getHospital());
                }

                // 就诊日期
                if (diagnosticInfo.getFirstClinicDate() != null) {
                    medicalInfo.append("，").append(DateUtil.formatDate(diagnosticInfo.getFirstClinicDate()));
                }

                // 诊断结论
                if (ObjectUtil.isNotEmpty(diagnosticInfo.getDiagnoses())) {
                    for (String diagnosis : diagnosticInfo.getDiagnoses()) {
                        if (StrUtil.isNotBlank(diagnosis)) {
                            medicalInfo.append("，").append(diagnosis);
                        }
                    }
                }

                // 在每个就诊记录结尾添加分号
                medicalInfo.append("；");
            }
        }
//		else {
//            // 兼容旧版单个诊断信息
//            if (StrUtil.isNotBlank(declaration.getHospital())) {
//                medicalInfo.append(declaration.getHospital());
//            }
//
//            if (declaration.getFirstClinicDate() != null) {
//                medicalInfo.append("，").append(DateUtil.formatDate(declaration.getFirstClinicDate()));
//            }
//
//            if (ObjectUtil.isNotEmpty(declaration.getDiagnoses())) {
//                for (Object diagnosis : declaration.getDiagnoses()) {
//                    if (diagnosis != null && StrUtil.isNotBlank(diagnosis.toString())) {
//                        medicalInfo.append("，").append(diagnosis.toString());
//                    }
//                }
//            }
//        }

        return medicalInfo.toString();
    }

    /**
     * 构建Cases就诊信息
     * 格式：就诊医院1，就诊日期1，诊断结论1，诊断结论2，诊断结论3；\n就诊医院2，就诊日期2，诊断结论1，诊断结论2，诊断结论3；
     */
    private String buildCasesMedicalInfo(Cases cases) {
        StringBuilder medicalInfo = new StringBuilder();

        // Cases表中的诊断信息
        if (ObjectUtil.isNotEmpty(cases.getDiagnosticInfoList())) {
            for (int i = 0; i < cases.getDiagnosticInfoList().size(); i++) {
                if (i > 0) {
                    medicalInfo.append("\n");
                }

                DiagnosticInfo diagnosticInfo = cases.getDiagnosticInfoList().get(i);

                // 就诊医院
                if (StrUtil.isNotBlank(diagnosticInfo.getHospital())) {
                    medicalInfo.append(diagnosticInfo.getHospital());
                }

                // 就诊日期
                if (diagnosticInfo.getFirstClinicDate() != null) {
                    medicalInfo.append("，").append(DateUtil.formatDate(diagnosticInfo.getFirstClinicDate()));
                }

                // 诊断结论
                if (ObjectUtil.isNotEmpty(diagnosticInfo.getDiagnoses())) {
                    for (String diagnosis : diagnosticInfo.getDiagnoses()) {
                        if (StrUtil.isNotBlank(diagnosis)) {
                            medicalInfo.append("，").append(diagnosis);
                        }
                    }
                }

                // 在每个就诊记录结尾添加分号
                medicalInfo.append("；");
            }
        }

        // 如果没有诊断信息列表，但有疾病名称，也显示出来
//        if (medicalInfo.length() == 0 && StrUtil.isNotBlank(cases.getDiseaseName())) {
//            medicalInfo.append("疾病名称：").append(cases.getDiseaseName());
//        }

        return medicalInfo.toString();
    }

    /**
     * 构建Declaration当前进度状态
     * 当子状态不为空时，直接使用子状态作为当前状态，不再拼接主状态
     */
    private String buildDeclarationCurrentStatus(Declaration declaration) {
        String status = declaration.getStatus();
        String subStatus = declaration.getSubStatus();

        // 如果子状态不为空，直接使用子状态
        if (StrUtil.isNotBlank(subStatus)) {
            switch (subStatus) {
                case "NoNeedApply":
                    return "无需申报";
                case "FurtherInfo":
                    return "补充资料";
                case "Auditing":
                    return "审核中";
                case "SuspendAuditing":
                    return "时效中止提交审核中";
                case "Canceling":
                    return "撤销申请";
                case "WaitMaterialSubmit":
                    return "待实物提交";
                case "Suspended":
                    return "时效终止";
                case "NotAccept":
                    return "不予受理";
                case "NotIdentify":
                    return "不予认定";
                default:
                    return subStatus;
            }
        }

        // 子状态为空时，使用主状态
        if (StrUtil.isNotBlank(status)) {
            switch (status) {
                case "Applying":
                    return "申报中";
                case "Accepting":
                    return "待受理";
                case "Identifying":
                    return "认定中";
                case "Classifying":
                    return "鉴定中";
                case "Done":
                    return "已办结";
                case "Canceling":
                    return "撤销中";
                case "Cancelled":
                    return "已撤销";
                case "Rejected":
                    return "已退回";
                default:
                    return status;
            }
        }

        return "";
    }

    /**
     * 构建Cases当前进度状态
     */
    private String buildCasesCurrentStatus(Cases cases) {
        String status = cases.getStatus();

        if (StrUtil.isNotBlank(status)) {
            switch (status) {
                case "Submitted":
                    return "已报备";
                case "Applying":
                    return "申报中";
                case "Applied":
                    return "已申报";
                case "Cancelled":
                    return "已撤销";
                default:
                    return status;
            }
        }

        return "";
    }

    /**
     * 生成文件名
     * 格式：用人单位名称-时间范围-导出日期（例：成都易训企业管理咨询有限公司-近3个月-20250724）
     */
    private String generateFileName(ApplyExportRequestVO requestVO, List<ApplyRecordExportVO> exportData) {
        StringBuilder fileName = new StringBuilder();

        // 用人单位名称（直接从requestVO获取）
        String organizationName = StrUtil.isNotBlank(requestVO.getOrganizationName()) ?
            requestVO.getOrganizationName() : "未知单位";

        fileName.append(organizationName);

        // 时间范围
        Integer timeRange = requestVO.getTimeRange() != null ? requestVO.getTimeRange() : 1;
        // 限制范围在1-12之间
        if (timeRange < 1) {
            timeRange = 1;
        } else if (timeRange > 12) {
            timeRange = 12;
        }
        fileName.append("-近").append(timeRange).append("个月");

        // 导出日期（只要日期，不要时间）
        String exportDate = DateUtil.format(new Date(), "yyyyMMdd");
        fileName.append("-").append(exportDate);

        fileName.append(".xlsx");

        return fileName.toString();
    }



    /**
     * 创建单元格样式策略，设置黑色边框
     */
    private HorizontalCellStyleStrategy createCellStyleStrategy() {
        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();

        // 设置边框样式为细线
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色为黑色
        contentWriteCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        contentWriteCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        contentWriteCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        contentWriteCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

        // 设置水平和垂直对齐方式
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 启用自动换行
        contentWriteCellStyle.setWrapped(true);

        // 头部样式（如果需要）
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);

        headWriteCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        headWriteCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        headWriteCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        headWriteCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 头部也启用自动换行
        headWriteCellStyle.setWrapped(true);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 将List转换为字符串
     */
    private String convertListToString(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            Object item = list.get(i);
            if (item != null) {
                if (item instanceof Map) {
                    Map<?, ?> map = (Map<?, ?>) item;
                    Object label = map.get("label");
                    if (label != null) {
                        sb.append(label.toString());
                    }
                } else {
                    sb.append(item.toString());
                }

                if (i < list.size() - 1) {
                    sb.append("、");
                }
            }
        }

        return sb.toString();
    }
}
