package com.yixun.wid.v2.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * AI快捷填充请求参数
 */
@Data
public class AiQuickFillRequestVO {

    /**
     * 材料识别结果
     * key: 材料类型（如"身份证"、"诊断证明"等）
     * value: 识别结果列表
     */
    @NotEmpty(message = "材料识别结果不能为空")
    private Map<String, List<MaterialRecognitionResult>> materialRecognitionResults;
}
