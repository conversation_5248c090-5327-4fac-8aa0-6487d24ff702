package com.yixun.wid.v2.bean.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 三目录相似搜索输入参数
 */
@Data
public class ThreeCatalogueSimilarSearchIn {
    
    /**
     * 目录类别（可选参数，可为空）
     * @deprecated 使用 types 替代
     */
    @Deprecated
    private String type;

    /**
     * 目录类别列表（可选参数，可为空）
     * 支持多个目录类别同时搜索，如血费对应诊疗服务和医用耗材
     */
    private List<String> types;
    
    /**
     * 项目名称（必填参数）
     */
    @NotBlank(message = "项目名称不能为空")
    private String projectName;
    
    /**
     * 返回结果数量（可选，默认为1）
     */
    private Integer topK = 1;
    
    /**
     * 相似度阈值（可选，默认为0.0）
     */
    private Double similarityThreshold = 0.0;
}
