package com.yixun.wid.v2.bean.in;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 账单明细批量修改费用类别输入参数
 */
@Data
public class BillingDetailBatchUpdateFeeTypeIn {
    
    /**
     * 账单明细ID列表
     */
    @NotEmpty(message = "账单明细ID列表不能为空")
    private List<Long> billingDetailIds;
    
    /**
     * 目标费用类别
     */
    @NotNull(message = "目标费用类别不能为空")
    private String targetFeeType;
}
