package com.yixun.wid.v2.bean.in;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 三目录批量添加输入参数
 */
@Data
public class ThreeCatalogueBatchAddIn {
    
    /**
     * 三目录数据列表
     */
    @NotEmpty(message = "三目录数据列表不能为空")
    @Valid
    private List<ThreeCatalogueAddItemIn> items;
    
    /**
     * 三目录添加项
     */
    @Data
    public static class ThreeCatalogueAddItemIn {
        
        /**
         * 目录编码
         */
        private String sn;
        
        /**
         * 项目名称（必填）
         */
        private String projectName;
        
        /**
         * 费用等级（必填：甲/乙/丙）
         */
        private String level;
        
        /**
         * 目录类别（必填：药品目录/诊疗服务/医用耗材）
         */
        private String type;
        
        /**
         * 开始日期（格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss）
         */
        private String startDate;
        
        /**
         * 结束日期（格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss）
         */
        private String endDate;
        
        /**
         * 限价医院等级
         */
        private String priceLimitLevel;
        
        /**
         * 定价上限金额(元)
         */
        private String maxPrice;
    }
}
