package com.yixun.wid.v2.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.wid.v2.bean.in.ThreeCatalogueSimilarSearchIn;
import com.yixun.wid.v2.entity.BillingDetail;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import com.yixun.wid.v2.service.ThreeCatalogueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 三目录相似搜索工具类
 * 提供费用类别转换和三目录搜索的通用方法
 */
@Slf4j
@Component
public class ThreeCatalogueSearchUtils {

    @Resource
    private ThreeCatalogueService threeCatalogueService;

    /**
     * 电子清单费用类别转换为三目录对应目录类别
     * 
     * @param feeType 电子清单费用类别
     * @return 三目录对应目录类别列表
     */
    public List<String> convertFeeTypeToThreeCatalogueTypes(String feeType) {
        if (StrUtil.isBlank(feeType)) {
            return new ArrayList<>();
        }
        
        List<String> types = new ArrayList<>();
        switch (feeType) {
            case "床位费":
                types.add("诊疗服务");
                break;
            case "药品费":
                types.add("药品目录");
                break;
            case "检查费":
                types.add("诊疗服务");
                break;
            case "治疗费":
                types.add("诊疗服务");
                break;
            case "材料费":
                types.add("医用耗材");
                break;
            case "血费":
                types.add("诊疗服务");
                types.add("医用耗材");
                break;
            default:
                // 如果没有匹配的转换规则，直接使用原费用类别
                types.add(feeType);
                break;
        }
        
        return types;
    }

    /**
     * 基于三目录相似搜索设置费用等级和三目录ID
     * 当费用等级为空且费用类别和项目名称不为空时，调用三目录相似搜索
     *
     * @param billingDetail 账单明细
     */
    public void setFeeLevelAndThreeCatalogueIdBySearch(BillingDetail billingDetail) {
        // 如果费用等级不为空，则不需要搜索
        if (StrUtil.isNotBlank(billingDetail.getFeeLevel())) {
            log.info("跳过三目录搜索：费用等级已存在，项目名称：{}，费用等级：{}",
                billingDetail.getProjectName(), billingDetail.getFeeLevel());
            return;
        }

        // 如果三目录ID不为空，则不需要搜索
        if (StrUtil.isNotBlank(billingDetail.getThreeCatalogueId())) {
            log.info("跳过三目录搜索：三目录ID已存在，项目名称：{}，三目录ID：{}",
                billingDetail.getProjectName(), billingDetail.getThreeCatalogueId());
            return;
        }

        // 检查费用类别和项目名称是否都不为空
        if (StrUtil.isBlank(billingDetail.getFeeType()) || StrUtil.isBlank(billingDetail.getProjectName())) {
            log.info("跳过三目录搜索：缺少必要参数，项目名称：{}，费用类别：{}", 
                billingDetail.getProjectName(), billingDetail.getFeeType());
            return;
        }

        try {
            // 将电子清单费用类别转换为三目录对应目录类别
            List<String> threeCatalogueTypes = convertFeeTypeToThreeCatalogueTypes(billingDetail.getFeeType());
            
            // 构建三目录相似搜索参数
            ThreeCatalogueSimilarSearchIn searchIn = new ThreeCatalogueSimilarSearchIn();
            searchIn.setProjectName(billingDetail.getProjectName());
            searchIn.setTypes(threeCatalogueTypes); // 使用转换后的目录类别列表
            searchIn.setTopK(1);
            searchIn.setSimilarityThreshold(0.0);

            // 调用三目录相似搜索
            ThreeCatalogue threeCatalogue = threeCatalogueService.similarSearch(searchIn);

            if (threeCatalogue != null && StrUtil.isNotBlank(threeCatalogue.getLevel())) {
                // 设置费用等级
                billingDetail.setFeeLevel(threeCatalogue.getLevel());
                // 设置三目录ID（冗余存储，将Long转换为String）
                if (threeCatalogue.getId() != null) {
                    billingDetail.setThreeCatalogueId(String.valueOf(threeCatalogue.getId()));
                }
                log.info("三目录相似搜索成功，项目名称：{}，费用类别：{}，转换后目录类别：{}，匹配到费用等级：{}，三目录ID：{}", 
                    billingDetail.getProjectName(), billingDetail.getFeeType(), threeCatalogueTypes, threeCatalogue.getLevel(), threeCatalogue.getId());
            } else {
                log.info("三目录相似搜索未找到匹配结果，项目名称：{}，费用类别：{}，转换后目录类别：{}", 
                    billingDetail.getProjectName(), billingDetail.getFeeType(), threeCatalogueTypes);
            }
        } catch (Exception e) {
            log.error("三目录相似搜索异常，项目名称：{}，费用类别：{}", 
                billingDetail.getProjectName(), billingDetail.getFeeType(), e);
        }

        // 如果费用等级仍然为空或空字符串，则设置为"未匹配"
        if (StrUtil.isBlank(billingDetail.getFeeLevel())) {
            billingDetail.setFeeLevel("未匹配");
            log.info("费用等级设置为未匹配，项目名称：{}，费用类别：{}", 
                billingDetail.getProjectName(), billingDetail.getFeeType());
        }
    }
}
