package com.yixun.wid.entity;

import com.yixun.wid.bean.out.UserAddressOut;
import com.yixun.wid.v2.vo.MaterialAuditResponseVO;
import com.yixun.wid.v2.vo.MaterialRecognitionResult;
import com.yixun.wid.v2.vo.RecommendedCase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class Declaration {

    private Long id;

    private Date createTime;

    private Date updateTime;

    @ApiModelProperty("是否锁定")
    private Boolean isLocked;

    @ApiModelProperty("是否admin参与了")
    private Boolean isAdminInvolved;

    @ApiModelProperty("撤销前状态")
    private String statusBeforeCancel;

    @ApiModelProperty("撤销前子状态")
    private String subStatusBeforeCancel;

    @ApiModelProperty("案件号")
    private String caseSn;

    @ApiModelProperty("上报方式")
    private String submitWay;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("原因")
    private String reason;

    //1、职工信息
    //报案人信息
    @ApiModelProperty("上报人的用户id")
    private Long userId;

    @ApiModelProperty("与当事人关系")
    private String relationship;

    @ApiModelProperty("与当事人关系详情")
    private String relationshipDetail;

    @ApiModelProperty("提交人的用户id")
    private Long submitUserId;

    @ApiModelProperty("申报人/经办人姓名")
    private String applicantName;

    @ApiModelProperty("申报人/经办人手机")
    private String applicantPhone;

    @ApiModelProperty("报案状态")
    private String status;

    @ApiModelProperty("申报子状态")
    private String subStatus;

    @ApiModelProperty("来源案件id")
    private Long casesId;

    //工伤当事人参保信息
    @ApiModelProperty("是否参保")
    private Boolean hasInsurance;

    @ApiModelProperty("参保地")
    private String insuranceAddress;

    //工伤当事人信息
    @ApiModelProperty("职工姓名")
    private String name;

    @ApiModelProperty("职工性别")
    private String gender;

    @ApiModelProperty("出生年月日")
    private Date birthdayDate;

    @ApiModelProperty("身份证")
    private String idCard;

    @ApiModelProperty("身份证地址")
    private String idCardAddr;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("是否退休")
    private Boolean isRetired;

    @ApiModelProperty("家庭区域")
    private List homeRegion;

    @ApiModelProperty("家庭详细地址")
    private String homeAddr;

    @ApiModelProperty("工作岗位")
    private List position;

    @ApiModelProperty("参加工作时间")
    private Date firstWorkDate;

    //2、单位信息
    @ApiModelProperty("单位id")
    private Long organizationId;

    @ApiModelProperty("单位名称")
    private String organization;

    @ApiModelProperty("单位区域")
    private List organizationRegion;

    @ApiModelProperty("单位地址")
    private String organizationAddr;

    @ApiModelProperty("单位地址经度")
    private Double organizationLongitude;

    @ApiModelProperty("单位地址纬度")
    private Double organizationLatitude;

    @ApiModelProperty("单位邮编")
    private String zipCode;

    @ApiModelProperty("单位法人")
    private String legalPerson;

    @ApiModelProperty("法人电话")
    private String legalPersonPhone;

    //3.事故信息
    //是否职业病相关
    @ApiModelProperty("是否职业病相关")
    private Boolean isOccupDiseaseRelated;

    @ApiModelProperty("职业病名称")
    private String occupDiseaseName;

    @ApiModelProperty("接触职业病危害岗位")
    private String occupDiseasePosition;

    @ApiModelProperty("接触职业病危害时间")
    private String occupDiseaseDate;

    //事故信息
    @ApiModelProperty("伤害部位")
    private List injuredPart;

    @ApiModelProperty("事故时间")
    private Date accidentTime;

    @ApiModelProperty("事故区域")
    private List accidentRegion;

    @ApiModelProperty("事故地点")
    private String accidentAddress;

    @ApiModelProperty("事故地点经度")
    private Double longitude;

    @ApiModelProperty("事故地点纬度")
    private Double latitude;

    @ApiModelProperty("事故经过")
    private String accidentDetail;

    @ApiModelProperty("事故原因")
    private String accidentCause;

    @ApiModelProperty("事发时从事的工作")
    private String workingOfAccident;

    @ApiModelProperty("补充说明")
    private String supplementary;

    //事故相关材料
    @ApiModelProperty("现场照片")
    private List pictures;

    @ApiModelProperty("现场视频")
    private List videos;

    @ApiModelProperty("受伤害部位图片")
    private List injuredPartPics;

    //诊断信息
    @ApiModelProperty("就诊医院id")
    private Long hospitalId;

    @ApiModelProperty("就诊医院")
    private String hospital;

    @ApiModelProperty("初诊日期")
    private Date firstClinicDate;

    @ApiModelProperty("诊断结果")
    private List diagnoses;

    @ApiModelProperty("住院科室")
    private String department;

    @ApiModelProperty("床位号")
    private String bedNumber;

    @ApiModelProperty("事故者状态")
    private String injuredStatus;

    //4.申报材料
    @ApiModelProperty("工伤(亡)认定申请表")
    private List injureDeclaration;

    @ApiModelProperty("受伤职工有效身份证明")
    private List idCardPics;

    @ApiModelProperty("与用人单位存在劳动关系的证明材料")
    private List laborRelation;

    @ApiModelProperty("用人单位事故调查报告书")
    private List orgSurveyReport;

    @ApiModelProperty("超退休年龄未领取养老金证明")
    private List unclaimedPensionProof;

    @ApiModelProperty("有无证人")
    private Boolean hasWitness;

    @ApiModelProperty("无证人申明")
    private List noWitnessDeclare;

    @ApiModelProperty("证人证言1")
    private List witnessTestimony1;

    @ApiModelProperty("证人证言2")
    private List witnessTestimony2;

    @ApiModelProperty("证人证言3")
    private List witnessTestimony3;

    @ApiModelProperty("证人证言4")
    private List witnessTestimony4;

    @ApiModelProperty("医疗就诊材料")
    private List medicalDiagnose;

    @ApiModelProperty("职业病诊断证明或鉴定书")
    private List occupDiseaseDiagnose;

    @ApiModelProperty("授权委托书")
    private List authorizationLetter;

    @ApiModelProperty("经办人身份证明")
    private List applicantCert;

    @ApiModelProperty("用人单位的营业证明")
    private List businessCertificate;

    @ApiModelProperty("工会介绍信")
    private List unionReference;

    @ApiModelProperty("医学死亡证明或火化证明")
    private List deathCremationCert;

    @ApiModelProperty("特殊认定情况")
    private String specialCondition;

    @ApiModelProperty("特殊认定")
    private SpecialIdentify specialIdentify;

    @ApiModelProperty("实物提交方式")
    private String materialSubmitWay;

    @ApiModelProperty("实物提交运单号")
    private List materialSubmitMailSn;

    @ApiModelProperty("通知领取方式")
    private String informReceiveWay;

	@ApiModelProperty("送达确认书")
	private List deliveryConfirmationFile;

    @ApiModelProperty("通知收件方式")
    private UserAddressOut informReceiveAddr;

    @ApiModelProperty("受理通知书签名")
    private String acceptInformSignature;

    @ApiModelProperty("通知确认收到签字")
    private String informReceiveSignature;

    @ApiModelProperty("通知领取运单号")
    private List informReceiveMailSn;

    @ApiModelProperty("认定工伤决定书")
    private String injureIdentification;

    @ApiModelProperty("不予认定工伤决定书")
    private String injureNotIdentify;

    @ApiModelProperty("伤害类型")
    private String injureType;

    @ApiModelProperty("伤害表现")
    private String injureExpression;

    @ApiModelProperty("伤残等级")
    private String injureLevel;

    @ApiModelProperty("撤销申请书")
    private List cancelApplication;

    @ApiModelProperty("撤销申请时间")
    private Date cancelApplyTime;

    @ApiModelProperty("撤销成功时间")
    private Date canceledTime;

    @ApiModelProperty("是否已经发起调查")
    private Boolean hasIssuedInvestigate;

    @ApiModelProperty("是否材料递交方式开放")
    private Boolean isSubmitWayOpen;

    //各种结论
    @ApiModelProperty("受理结论")
    private AcceptConclusion acceptConclusion;

    @ApiModelProperty("认定结论")
    private IdentifyConclusion identifyConclusion;

    @ApiModelProperty("退回结论")
    private RejectConclusion rejectConclusion;

    @ApiModelProperty("认定中止结论")
    private SuspendedConclusion suspendedConclusion;

    @ApiModelProperty("认定中止结论")
    private CancelConclusion cancelConclusion;

    @ApiModelProperty("认定中止材料")
    private List suspendFileList;

    @ApiModelProperty("申请工伤认定提交材料目录 文件名称")
    private String materialCatalogUrl;

    @ApiModelProperty("工伤认定材料清单 文件名称")
    private String materialListUrl;

    @ApiModelProperty("工伤认定申请表 文件名称")
    private String applyUrl;

	@ApiModelProperty("诊断信息")
	private List<DiagnosticInfo> diagnosticInfoList;

	@ApiModelProperty("单位注册住所-单位区域")
	private List organizationRegionV2;

	@ApiModelProperty("单位注册住所-单位地址")
	private String organizationAddrV2;

	@ApiModelProperty("单位注册住所-单位地址经度")
	private Double organizationLongitudeV2;

	@ApiModelProperty("单位注册住所-单位地址纬度")
	private Double organizationLatitudeV2;

	@ApiModelProperty("工伤认定申请材料")
	private List declarationMaterials;

    @ApiModelProperty("伤者授权委托书")
    private List authorizationLetterV2;

	@ApiModelProperty("证明工伤事件的其他材料")
	private List otherMaterials;


	@ApiModelProperty("事故责任认定书")
	private List acciRespIdentify;

	@ApiModelProperty("路线图")
	private List routeMap;

	@ApiModelProperty("居住证明")
	private List residenceProof;

//	@ApiModelProperty("医学死亡证明或火化证明")
//	private List deathCremationCert;

	@ApiModelProperty("法院判决书，公安机关证明或其他证明")
	private List courtPoliceCert;

	@ApiModelProperty("民政部门或其他部门的证明")
	private List civilAffairsCert;

	@ApiModelProperty("伤残军人证")
	private List disabledMilitaryCert;

	@ApiModelProperty("旧伤复发鉴定证明")
	private List recurrenceCert;

	@ApiModelProperty("公安机关证明或者其它有效证明")
	private List publicSecurityCert;

	@ApiModelProperty("因工外出证明")
	private List workTravelCert;

	@ApiModelProperty("人民法院宣告死亡的结论")
	private List courtDeathCert;

//	@ApiModelProperty("因工外出证明")
//	private List workTravelCert;

	@ApiModelProperty("其他补充说明")
	private String otherSupplementary;

	@ApiModelProperty("证明工伤事件的其他材料")
	private List otherCert;

    @ApiModelProperty("案件申请时间")
    private Date submitTime;

	/**
	 * AI材料类型识别结果保存
	 */
	private Map<String, List<MaterialRecognitionResult>> materialRecognitionResults;

	/**
	 * AI材料类型识别保存
	 */
	private MaterialAuditResponseVO materialAudit;

	/**
	 * AI案例推荐保存
	 */
	private List<RecommendedCase> recommendedCases;


}
