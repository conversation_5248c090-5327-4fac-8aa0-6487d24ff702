package com.yixun.wid.v2.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yixun.wid.v2.entity.BillingDetail;
import com.yixun.wid.v2.entity.BillingInfo;
import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.service.PriceLimitCheckService;
import com.yixun.wid.v2.service.PriceLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 限价检查服务实现类
 */
@Slf4j
@Service
public class PriceLimitCheckServiceImpl implements PriceLimitCheckService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private PriceLimitService priceLimitService;

    @Override
    public void checkAndSetPriceLimitDeduction(BillingDetail billingDetail) {
        // 检查是否满足甲类费用限价检查条件
        if (!billingDetail.shouldCheckPriceLimit()) {
            return;
        }

        try {
            // 通过billingInfoId获取BillingInfo，从中获取priceLimitLevel
            BillingInfo billingInfo = mongoTemplate.findById(billingDetail.getBillingInfoId(), BillingInfo.class);
            if (billingInfo == null || billingInfo.getPriceLimitLevel() == null) {
                log.warn("未找到账单信息或限价医院等级为空，账单信息ID：{}", billingDetail.getBillingInfoId());
                return;
            }

            String priceLimitLevelStr = billingInfo.getPriceLimitLevel();
            if (StrUtil.isBlank(priceLimitLevelStr)) {
                log.warn("账单信息中限价医院等级为空，账单信息ID：{}", billingDetail.getBillingInfoId());
                return;
            }

            Integer priceLimitLevel;
            try {
                priceLimitLevel = Integer.valueOf(priceLimitLevelStr);
            } catch (NumberFormatException e) {
                log.warn("账单信息中限价医院等级格式错误，账单信息ID：{}，限价等级：{}",
                    billingDetail.getBillingInfoId(), priceLimitLevelStr);
                return;
            }
            Long threeCatalogueId = Long.valueOf(billingDetail.getThreeCatalogueId());

            // 通过三目录ID和限价医院等级查询限价目录信息
            PriceLimit priceLimit = priceLimitService.findByThreeCatalogueIdAndPriceLimitLevel(
                threeCatalogueId, priceLimitLevel);

            if (priceLimit != null && priceLimit.getMaxPrice() != null &&
                priceLimit.getMaxPrice().compareTo(BigDecimal.ZERO) > 0) {

                // 计算超出限价的金额：超出金额 = (单价 - 限价) × 数量
                BigDecimal exceedAmount = billingDetail.getUnitPrice().subtract(priceLimit.getMaxPrice());
                if (exceedAmount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal totalExceedAmount = exceedAmount.multiply(new BigDecimal(billingDetail.getQuantity()));

                    // 将不可报销金额设置为超出金额，扣减类型设置为审核扣减
                    billingDetail.setNonReimbursableAmount(totalExceedAmount);
                    billingDetail.setDeductionType("审核扣减");

                    log.info("甲类费用超出限价，项目名称：{}，单价：{}，限价：{}，超出金额：{}",
                        billingDetail.getProjectName(), billingDetail.getUnitPrice(),
                        priceLimit.getMaxPrice(), totalExceedAmount);
                } else {
                    log.debug("甲类费用未超出限价，项目名称：{}，单价：{}，限价：{}",
                        billingDetail.getProjectName(), billingDetail.getUnitPrice(), priceLimit.getMaxPrice());
                }
            } else {
                log.warn("未找到对应的限价信息，项目名称：{}，三目录ID：{}，限价医院等级：{}",
                    billingDetail.getProjectName(), threeCatalogueId, priceLimitLevel);
            }
        } catch (Exception e) {
            log.error("限价检查异常，项目名称：{}，三目录ID：{}",
                billingDetail.getProjectName(), billingDetail.getThreeCatalogueId(), e);
        }
    }




}
