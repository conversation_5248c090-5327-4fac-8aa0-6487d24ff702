package com.yixun.wid.v2.bean.in;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 账单明细批量修改不可报销金额输入参数
 */
@Data
public class BillingDetailBatchUpdateReimbursableIn {
    
    /**
     * 账单明细ID列表
     */
    @NotEmpty(message = "账单明细ID列表不能为空")
    private List<Long> billingDetailIds;
    
    /**
     * 操作类型
     * 0 - 可报销
     * 1 - 不可报销
     * 2 - 不可报销(非工伤)
     */
    @NotNull(message = "操作类型不能为空")
    private Integer operationType;
}
