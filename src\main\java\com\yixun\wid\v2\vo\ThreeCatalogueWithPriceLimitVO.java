package com.yixun.wid.v2.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 三目录信息及其对应的限价信息
 */
@Data
public class ThreeCatalogueWithPriceLimitVO {

    /**
     * 三目录id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long threeCatalogueId;

    /**
     * 限价目录id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long priceLimitId;

    /**
     * 目录编码
     */
    private String sn;

    /**
     * 目录类别
     */
    private String type;

    /**
     * 费用等级
     */
    private String level;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endDate;

    /**
     * 限价金额(元)
     */
    private BigDecimal maxPrice;

    /**
     * 匹配状态（是否来自电子清单关联的三目录）
     * true - 来自电子清单关联的三目录
     * false - 来自查询条件匹配的三目录
     */
    private Boolean matched = false;

    /**
     * 异常状态（三目录是否已过期）
     * true - 三目录已过期（门诊开始时间在三目录结束日期之后）
     * false - 三目录正常有效
     */
    private Boolean abnormal = false;
}
