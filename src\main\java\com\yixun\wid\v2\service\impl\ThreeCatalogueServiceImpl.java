package com.yixun.wid.v2.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.wid.v2.bean.in.ThreeCatalogueSimilarSearchIn;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import com.yixun.wid.v2.service.ThreeCatalogueService;
import com.yixun.wid.v2.utils.AiUtils;
import com.yixun.wid.v2.vo.ai.SimilarityResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 三目录服务实现类
 */
@Slf4j
@Service
public class ThreeCatalogueServiceImpl implements ThreeCatalogueService {
    
    @Resource
    private MongoTemplate mongoTemplate;
    
    @Resource
    private AiUtils aiUtils;
    
    @Override
    public ThreeCatalogue similarSearch(ThreeCatalogueSimilarSearchIn searchIn) {
        try {
            // 构建查询条件
            Query query = new Query();

            // 如果目录类别不为空，先根据类别筛选对应的三目录列表
            // 优先使用types列表，如果为空则使用type字段（向后兼容）
            if (CollUtil.isNotEmpty(searchIn.getTypes())) {
                query.addCriteria(Criteria.where("type").in(searchIn.getTypes()));
            } else if (StrUtil.isNotBlank(searchIn.getType())) {
                query.addCriteria(Criteria.where("type").is(searchIn.getType()));
            }
            
            // 查询三目录数据，只获取项目名称字段用于相似度比较
            query.fields().include("projectName");
            List<ThreeCatalogue> catalogues = mongoTemplate.find(query, ThreeCatalogue.class);
            
            if (CollUtil.isEmpty(catalogues)) {
                log.info("未找到匹配的三目录数据，搜索条件：{}", searchIn);
                return null;
            }
            
            // 提取项目名称列表
            List<String> targetNames = catalogues.stream()
                    .map(ThreeCatalogue::getProjectName)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            
            if (CollUtil.isEmpty(targetNames)) {
                log.info("三目录数据中未找到有效的项目名称，搜索条件：{}", searchIn);
                return null;
            }
            
            // 使用AI工具进行相似度匹配
            List<SimilarityResult> similarResults = aiUtils.findSimilarItems(
                    searchIn.getProjectName(), 
                    targetNames, 
                    searchIn.getTopK(), 
                    searchIn.getSimilarityThreshold()
            );
            
            if (CollUtil.isEmpty(similarResults)) {
                log.info("未找到相似度满足条件的三目录项目，搜索条件：{}", searchIn);
                return null;
            }
            
            // 获取相似度最高的项目名称
            String mostSimilarProjectName = similarResults.get(0).getName();
            log.info("找到最相似的项目名称：{}，相似度：{}", mostSimilarProjectName, similarResults.get(0).getSimilarity());
            
            // 根据项目名称查询完整的三目录详情
            Query detailQuery = new Query();
            detailQuery.addCriteria(Criteria.where("projectName").is(mostSimilarProjectName));

            // 如果指定了目录类别，也要加入查询条件
            // 优先使用types列表，如果为空则使用type字段（向后兼容）
            if (CollUtil.isNotEmpty(searchIn.getTypes())) {
                detailQuery.addCriteria(Criteria.where("type").in(searchIn.getTypes()));
            } else if (StrUtil.isNotBlank(searchIn.getType())) {
                detailQuery.addCriteria(Criteria.where("type").is(searchIn.getType()));
            }
            
            // 按创建时间倒序，获取最新的一条记录
            detailQuery.with(Sort.by(Sort.Direction.DESC, "createTime"));
            
            ThreeCatalogue result = mongoTemplate.findOne(detailQuery, ThreeCatalogue.class);
            
            if (result == null) {
                log.warn("根据项目名称未找到匹配的三目录详情，项目名称：{}，搜索条件：{}", mostSimilarProjectName, searchIn);
                return null;
            }
            
            log.info("三目录相似搜索成功，返回结果：{}", result.getProjectName());
            return result;
            
        } catch (Exception e) {
            log.error("三目录相似搜索异常，搜索条件：{}", searchIn, e);
            return null;
        }
    }
}
