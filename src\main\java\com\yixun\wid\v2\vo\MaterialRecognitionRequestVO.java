package com.yixun.wid.v2.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 材料类型识别请求参数
 */
@Data
public class MaterialRecognitionRequestVO {

    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    /**
     * 需要识别的文件URL列表
     */
    @NotEmpty(message = "文件URL列表不能为空")
    private List<String> fileUrls;
}
