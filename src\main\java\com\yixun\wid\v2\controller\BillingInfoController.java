package com.yixun.wid.v2.controller;

import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.v2.bean.in.BillingInfoBatchDeleteIn;
import com.yixun.wid.v2.bean.in.BillingInfoIn;
import com.yixun.wid.v2.bean.in.CheckHospitalNatureIn;
import com.yixun.wid.v2.bean.in.UpdateBillingPriceLimitLevelIn;
import com.yixun.wid.v2.entity.BillingDetail;
import com.yixun.wid.v2.entity.BillingInfo;
import com.yixun.wid.v2.entity.MedicalInstitutions;
import com.yixun.wid.v2.service.BillingInfoService;
import com.yixun.wid.v2.service.MedicalInstitutionsService;
import com.yixun.wid.v2.service.PriceLimitCheckService;
import com.yixun.wid.v2.vo.CheckHospitalNatureResponse;
import com.yixun.wid.v2.vo.UpdatePriceLimitLevelIn;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.MongoTemplate;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 待遇 账单信息相关接口
 */
@Slf4j
@RestController
@RequestMapping("/v2/billingInfo")
public class BillingInfoController {

    @Resource
    private BillingInfoService billingInfoService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private MedicalInstitutionsService medicalInstitutionsService;

    @Resource
    private PriceLimitCheckService priceLimitCheckService;

    /**
     * 分页查询账单信息
     *
     * @param medicalCasesId 关联的工伤待遇业务ID
     * @param hospital 医院名称（模糊查询）
     * @param treatmentType 治疗类型
     * @param commonPage 分页参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public CommonResult<List<BillingInfo>> list(
            @RequestParam(required = false) Long medicalCasesId,
            @RequestParam(required = false) String hospital,
            @RequestParam(required = false) String treatmentType,
            CommonPage commonPage) {

        List<BillingInfo> billingInfos = billingInfoService.list(medicalCasesId, hospital, treatmentType, commonPage);

        return CommonResult.successPageData(billingInfos, commonPage);
    }

    /**
     * 根据ID获取账单信息详情
     *
     * @param id 账单信息ID
     * @return 账单信息详情
     */
    @GetMapping("/detail")
    public CommonResult<BillingInfo> getDetail(@RequestParam Long id) {
        BillingInfo billingInfo = billingInfoService.getDetail(id);
        return CommonResult.successData(billingInfo);
    }

    /**
     * 新增账单信息
     *
     * @param billingInfoIn 账单信息输入参数
     * @return 新增结果
     */
    @PostMapping("/add")
    public CommonResult<BillingInfo> add(@RequestBody BillingInfoIn billingInfoIn) {
        BillingInfo billingInfo = billingInfoService.add(billingInfoIn);
        return CommonResult.successData(billingInfo);
    }

    /**
     * 更新账单信息
     *
     * @param billingInfo 账单信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<BillingInfo> update(@RequestBody BillingInfo billingInfo) {
        BillingInfo updatedBillingInfo = billingInfoService.update(billingInfo);
        return CommonResult.successData(updatedBillingInfo);
    }

    /**
     * 批量删除账单信息
     *
     * @param batchDeleteIn 批量删除参数
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Long> batchDelete(@RequestBody BillingInfoBatchDeleteIn batchDeleteIn) {
        Long deletedCount = billingInfoService.batchDelete(batchDeleteIn);
        return CommonResult.successData(deletedCount);
    }

    /**
     * 根据工伤待遇业务ID查询关联的账单信息列表
     *
     * @param medicalCasesId 工伤待遇业务ID
     * @return 账单信息列表
     */
    @GetMapping("/listByMedicalCases")
    public CommonResult<List<BillingInfo>> listByMedicalCases(@RequestParam Long medicalCasesId) {
        List<BillingInfo> billingInfos = billingInfoService.listByMedicalCases(medicalCasesId);
        return CommonResult.successData(billingInfos);
    }

    /**
     * 修改账单的限价医院等级
     *
     * @param updateRequest 修改请求参数
     * @return 操作结果
     */
    @PostMapping("/updatePriceLimitLevel")
    public CommonResult<String> updatePriceLimitLevel(@RequestBody UpdateBillingPriceLimitLevelIn updateRequest) {
        // 参数验证
        if (updateRequest.getBillingInfoId() == null) {
            return CommonResult.failResult(10001, "账单ID不能为空");
        }
	    String priceLimitLevel = updateRequest.getPriceLimitLevel();
	    if (StrUtil.isBlank(priceLimitLevel)) {
            return CommonResult.failResult(10001, "限价医院等级不能为空");
        }
        // 查询账单信息
        BillingInfo billingInfo = mongoTemplate.findById(updateRequest.getBillingInfoId(), BillingInfo.class);
        if (billingInfo == null) {
            return CommonResult.failResult(10001, "账单信息不存在");
        }

        // 记录原始值
        String originalPriceLimitLevel = billingInfo.getPriceLimitLevel();

        // 更新账单的限价医院等级
        billingInfo.setPriceLimitLevel(priceLimitLevel);
        billingInfo.setUpdateTime(new java.util.Date());
        mongoTemplate.save(billingInfo);

        log.info("更新账单限价医院等级成功，账单ID：{}，原等级：{}，新等级：{}",
            updateRequest.getBillingInfoId(), originalPriceLimitLevel, priceLimitLevel);

        // 对满足shouldCheckPriceLimit条件的BillingDetail数据进行甲类费用限价检查并更新
        processPriceLimitCheckForBillingDetails(updateRequest.getBillingInfoId());

        // 如果提供了医疗机构ID，同时更新医疗机构的限价等级
        if (updateRequest.getMedicalInstitutionId() != null) {
            UpdatePriceLimitLevelIn medicalUpdateRequest = new UpdatePriceLimitLevelIn();
            medicalUpdateRequest.setHospitalId(updateRequest.getMedicalInstitutionId());
            medicalUpdateRequest.setPriceLimitLevel(priceLimitLevel);

            medicalInstitutionsService.updatePriceLimitLevel(medicalUpdateRequest);
            log.info("同时更新医疗机构限价等级成功，医疗机构ID：{}，等级：{}",
                updateRequest.getMedicalInstitutionId(), priceLimitLevel);
        }

        return CommonResult.successResult("操作成功");
    }

    /**
     * 对满足shouldCheckPriceLimit条件的BillingDetail数据进行甲类费用限价检查并更新
     *
     * @param billingInfoId 账单信息ID
     */
    private void processPriceLimitCheckForBillingDetails(Long billingInfoId) {
        try {
            // 构建查询条件，对应shouldCheckPriceLimit方法的条件：
            // isWorkInjury = true && feeLevel = "甲" && billingInfoId != null && threeCatalogueId != null && unitPrice != null && quantity != null
            Query query = new Query();
            query.addCriteria(Criteria.where("billingInfoId").is(billingInfoId));
            query.addCriteria(Criteria.where("isWorkInjury").is(true));
            query.addCriteria(Criteria.where("feeLevel").is("甲"));
            query.addCriteria(Criteria.where("threeCatalogueId").ne(null).ne(""));
            query.addCriteria(Criteria.where("unitPriceInCent").ne(null));
            query.addCriteria(Criteria.where("quantity").ne(null));

            // 查询满足条件的BillingDetail数据
            List<BillingDetail> billingDetails = mongoTemplate.find(query, BillingDetail.class);

            log.info("找到{}条满足甲类费用限价检查条件的账单明细，账单ID：{}", billingDetails.size(), billingInfoId);

            // 对每条数据进行限价检查并更新
            int updatedCount = 0;
            for (BillingDetail billingDetail : billingDetails) {
                try {
                    // 调用限价检查服务
                    priceLimitCheckService.checkAndSetPriceLimitDeduction(billingDetail);

                    // 保存更新后的数据
                    mongoTemplate.save(billingDetail);
                    updatedCount++;
                } catch (Exception e) {
                    log.error("处理账单明细限价检查异常，明细ID：{}，项目名称：{}",
                        billingDetail.getId(), billingDetail.getProjectName(), e);
                }
            }

            log.info("完成甲类费用限价检查，账单ID：{}，处理明细数：{}，成功更新数：{}",
                billingInfoId, billingDetails.size(), updatedCount);

        } catch (Exception e) {
            log.error("批量处理账单明细限价检查异常，账单ID：{}", billingInfoId, e);
        }
    }

    /**
     * 检查医院性质
     * 根据医疗机构ID和门诊开始时间判断医院性质
     *
     * @param request 检查医院性质请求参数
     * @return 医院性质检查结果
     */
    @PostMapping("/checkHospitalNature")
    public CommonResult<CheckHospitalNatureResponse> checkHospitalNature(@Valid @RequestBody CheckHospitalNatureIn request) {
        try {
            Long medicalInstitutionId = request.getMedicalInstitutionId();
            Date outpatientStartTime = request.getOutpatientStartTime();

            // 根据医疗机构ID查询医疗机构信息
            MedicalInstitutions medicalInstitution = mongoTemplate.findById(medicalInstitutionId, MedicalInstitutions.class);

            String hospitalNature;

            if (medicalInstitution == null) {
                // 未查询到医疗机构信息
                hospitalNature = "其他";
            } else {
                // 检查医疗机构状态
                if (Boolean.FALSE.equals(medicalInstitution.getEnabled())) {
                    // 医疗机构状态为禁用
                    hospitalNature = "定点医院（禁用）";
                } else if (Boolean.FALSE.equals(medicalInstitution.getIsAgreementHospital())) {
                    // 识别为非协议机构
                    hospitalNature = "非定点医院";
                } else {
                    // 判断门诊开始时间与医疗机构有效期的关系
                    hospitalNature = determineHospitalNatureByDate(medicalInstitution, outpatientStartTime);
                }
            }

            CheckHospitalNatureResponse response = new CheckHospitalNatureResponse(hospitalNature);
            return CommonResult.successData(response);

        } catch (Exception e) {
            log.error("检查医院性质异常，医疗机构ID：{}，门诊开始时间：{}",
                request.getMedicalInstitutionId(), request.getOutpatientStartTime(), e);
            return CommonResult.failResult(10001, "检查医院性质失败：" + e.getMessage());
        }
    }

    /**
     * 根据日期判断医院性质
     *
     * @param medicalInstitution 医疗机构信息
     * @param outpatientStartTime 门诊开始时间
     * @return 医院性质
     */
    private String determineHospitalNatureByDate(MedicalInstitutions medicalInstitution, Date outpatientStartTime) {
        Date startDate = medicalInstitution.getStartDate();
        Date endDate = medicalInstitution.getEndDate();

        // 如果开始日期为空，认为一直有效（从很早开始）
        if (startDate != null && outpatientStartTime.before(startDate)) {
            return "定点医院（未开始）";
        }

        // 如果结束日期为空，认为一直有效（到很晚结束）
        if (endDate != null && outpatientStartTime.after(endDate)) {
            return "定点医院（已终止）";
        }

        // 在有效期内
        return "定点医院";
    }
}
