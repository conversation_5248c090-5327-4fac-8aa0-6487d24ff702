package com.yixun.wid.v2.service;

import com.yixun.wid.v2.entity.PriceLimit;

/**
 * 限价目录服务接口
 */
public interface PriceLimitService {
    
    /**
     * 根据三目录ID和限价医院等级查询限价信息
     * 
     * @param threeCatalogueId 三目录ID
     * @param priceLimitLevel 限价医院等级
     * @return 限价信息，如果没有找到返回null
     */
    PriceLimit findByThreeCatalogueIdAndPriceLimitLevel(Long threeCatalogueId, Integer priceLimitLevel);
}
