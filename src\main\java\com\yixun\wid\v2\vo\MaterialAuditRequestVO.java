package com.yixun.wid.v2.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 材料审核请求参数
 */
@Data
public class MaterialAuditRequestVO {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空")
    private Long id;

    /**
     * 类型：declaration 或 cases
     */
    @NotBlank(message = "类型不能为空")
    @Pattern(regexp = "^(declaration|cases)$", message = "类型只能是declaration或cases")
    private String type;
}
