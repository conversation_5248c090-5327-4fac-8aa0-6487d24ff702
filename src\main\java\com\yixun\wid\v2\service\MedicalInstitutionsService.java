package com.yixun.wid.v2.service;

import com.yixun.wid.v2.vo.UpdatePriceLimitLevelIn;

/**
 * 医疗机构服务接口
 */
public interface MedicalInstitutionsService {

    /**
     * 更新医院的限价医院等级
     * 只有当医院的限价医院等级为空时才会实际更新，如果已有值则跳过更新
     *
     * @param updatePriceLimitLevelIn 更新参数
     * @throws RuntimeException 当医院不存在时抛出异常
     */
    void updatePriceLimitLevel(UpdatePriceLimitLevelIn updatePriceLimitLevelIn);
}
