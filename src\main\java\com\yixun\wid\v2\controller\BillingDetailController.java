package com.yixun.wid.v2.controller;

import cn.hutool.core.util.StrUtil;
import com.mongodb.client.result.DeleteResult;
import com.yixun.bean.CommonPage;
import com.yixun.bean.CommonResult;
import com.yixun.wid.utils.MongoUtil;
import com.yixun.wid.utils.SnGeneratorUtil;
import com.yixun.wid.v2.bean.in.BillingDetailBatchAddIn;
import com.yixun.wid.v2.bean.in.BillingDetailBatchDeleteIn;
import com.yixun.wid.v2.bean.in.BillingDetailBatchUpdateReimbursableIn;
import com.yixun.wid.v2.bean.in.BillingDetailIn;
import com.yixun.wid.v2.bean.in.BillingDetailMatchThreeCatalogueIn;
import com.yixun.wid.v2.entity.BillingDetail;
import com.yixun.wid.v2.entity.BillingInfo;
import com.yixun.wid.v2.entity.ThreeCatalogue;
import com.yixun.wid.v2.utils.ThreeCatalogueSearchUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 待遇 电子清单-账单明细相关接口
 */
@Slf4j
@RestController
@RequestMapping("/v2/billingDetail")
public class BillingDetailController {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ThreeCatalogueSearchUtils threeCatalogueSearchUtils;

    /**
     * 分页查询账单明细
     *
     * @param billingInfoId 关联的账单信息ID
     * @param projectName   项目名称（模糊查询）
     * @param projectCode   项目编码
     * @param feeType       费用类别
     * @param feeLevel      费用等级
     * @param isWorkInjury  是否工伤
     * @param commonPage    分页参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public CommonResult<List<BillingDetail>> list(
            @RequestParam(required = false) Long billingInfoId,
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String projectCode,
            @RequestParam(required = false) String feeType,
            @RequestParam(required = false) String feeLevel,
            @RequestParam(required = false) Boolean isWorkInjury,
            CommonPage commonPage) {

        Query query = new Query();

        // 账单信息ID查询
        if (billingInfoId != null) {
            query.addCriteria(Criteria.where("billingInfoId").is(billingInfoId));
        }

        // 项目名称模糊查询
        if (StringUtils.hasText(projectName)) {
            query.addCriteria(Criteria.where("projectName").regex(".*" + projectName + ".*", "i"));
        }

        // 项目编码精确匹配
        if (StringUtils.hasText(projectCode)) {
            query.addCriteria(Criteria.where("projectCode").is(projectCode));
        }

        // 费用类别精确匹配
        if (StringUtils.hasText(feeType)) {
            query.addCriteria(Criteria.where("feeType").is(feeType));
        }

        // 费用等级精确匹配
        if (StringUtils.hasText(feeLevel)) {
            query.addCriteria(Criteria.where("feeLevel").is(feeLevel));
        }

        // 是否工伤
        if (isWorkInjury != null) {
            query.addCriteria(Criteria.where("isWorkInjury").is(isWorkInjury));
        }

        // 添加排序：先按orderNum升序排序，再按创建时间降序
        query.with(Sort.by(Sort.Direction.ASC, "orderNum").and(Sort.by(Sort.Direction.DESC, "createTime")));

        // 设置分页信息
        MongoUtil.setPageInfo(mongoTemplate, BillingDetail.class, query, commonPage);

        // 执行查询
        List<BillingDetail> billingDetails = mongoTemplate.find(query, BillingDetail.class);

        return CommonResult.successPageData(billingDetails, commonPage);
    }

    /**
     * 根据ID获取账单明细详情
     *
     * @param id 账单明细ID
     * @return 账单明细详情
     */
    @GetMapping("/detail")
    public CommonResult<BillingDetail> getDetail(@RequestParam Long id) {
        BillingDetail billingDetail = mongoTemplate.findById(id, BillingDetail.class);
        if (billingDetail == null) {
            throw new RuntimeException("账单明细不存在");
        }
        return CommonResult.successData(billingDetail);
    }

    /**
     * 新增账单明细
     *
     * @param billingDetailIn 账单明细输入参数
     * @return 新增结果
     */
    @PostMapping("/add")
    public CommonResult<BillingDetail> add(@RequestBody BillingDetailIn billingDetailIn) {
        // 验证关联的账单信息是否存在
        Long billingInfoId = billingDetailIn.getBillingInfoId();
        BillingInfo billingInfo = mongoTemplate.findById(billingInfoId, BillingInfo.class);
        if (billingInfo == null) {
            return CommonResult.failResult(10001, "关联的账单信息不存在");
        }

        // 创建新的账单明细对象
        BillingDetail billingDetail = new BillingDetail();
        BeanUtils.copyProperties(billingDetailIn, billingDetail);

        // 处理orderNum，如果未提供序号，则根据已有明细数量自动设置
        if (billingDetail.getOrderNum() == null) {
            // 查询当前账单信息关联的明细数量
            Query countQuery = Query.query(Criteria.where("billingInfoId").is(billingInfoId));
            long existingDetailsCount = mongoTemplate.count(countQuery, BillingDetail.class);
            billingDetail.setOrderNum((int)existingDetailsCount + 1);
        }

        // 生成唯一ID
        billingDetail.setId(SnGeneratorUtil.getId());

        // 设置创建时间和更新时间
        Date now = new Date();
        billingDetail.setCreateTime(now);
        billingDetail.setUpdateTime(now);

	    // 如果费用等级为空，且费用类别和项目名称不为空，调用三目录相似搜索
	    threeCatalogueSearchUtils.setFeeLevelAndThreeCatalogueIdBySearch(billingDetail);

	    // 自动计算金额、数量、单价
	    billingDetail.autoCalculate();

        // 保存到数据库
        mongoTemplate.save(billingDetail);

        return CommonResult.successData(billingDetail);
    }

    /**
     * 批量添加或修改账单明细
     *
     * @param batchAddIn 批量添加或修改参数
     * @return 操作结果
     */
    @PostMapping("/batchAdd")
    public CommonResult<List<BillingDetail>> batchAddOrUpdate(@RequestBody BillingDetailBatchAddIn batchAddIn) {
        // 验证关联的账单信息是否存在
        Long billingInfoId = batchAddIn.getBillingInfoId();
        BillingInfo billingInfo = mongoTemplate.findById(billingInfoId, BillingInfo.class);
        if (billingInfo == null) {
            return CommonResult.failResult(10001, "关联的账单信息不存在");
        }

        // 查询当前账单信息关联的明细数量，用于自动设置序号
        Query countQuery = Query.query(Criteria.where("billingInfoId").is(billingInfoId));
        long existingDetailsCount = mongoTemplate.count(countQuery, BillingDetail.class);

        List<BillingDetail> savedDetails = new ArrayList<>();
        Date now = new Date();

        // 处理每个账单明细
        int newItemIndex = 0; // 用于为新增明细分配序号
        for (BillingDetailBatchAddIn.BillingDetailItem item : batchAddIn.getBillingDetails()) {
            BillingDetail billingDetail;
            boolean isUpdate = false;

            // 判断是新增还是修改
            if (item.getId() != null) {
                // 尝试查找现有记录
                billingDetail = mongoTemplate.findById(item.getId(), BillingDetail.class);
                if (billingDetail != null) {
                    // 找到现有记录，进行更新
                    isUpdate = true;
                    // 保存原有的关键信息
                    Date originalCreateTime = billingDetail.getCreateTime();
                    Long originalId = billingDetail.getId();
                    String originalFeeType = billingDetail.getFeeType();
                    String originalProjectName = billingDetail.getProjectName();

                    // 复制属性，但保留原有的创建时间和ID
                    BeanUtils.copyProperties(item, billingDetail);
                    billingDetail.setId(originalId);
                    billingDetail.setCreateTime(originalCreateTime);

                    // 检查费用类别和项目名称是否发生变化，如果变化则清空三目录ID
                    boolean feeTypeChanged = !Objects.equals(originalFeeType, billingDetail.getFeeType());
                    boolean projectNameChanged = !Objects.equals(originalProjectName, billingDetail.getProjectName());

                    if (feeTypeChanged || projectNameChanged) {
                        log.info("批量更新：费用类别或项目名称发生变化，清空三目录ID，电子清单ID：{}，项目名称：{} -> {}，费用类别：{} -> {}",
                            billingDetail.getId(), originalProjectName, billingDetail.getProjectName(),
                            originalFeeType, billingDetail.getFeeType());
                        billingDetail.setThreeCatalogueId(null);
                        billingDetail.setFeeLevel(null); // 同时清空费用等级，让其重新匹配
                    }
                } else {
                    // ID存在但记录不存在，按新增处理
                    billingDetail = new BillingDetail();
                    BeanUtils.copyProperties(item, billingDetail);
                    billingDetail.setId(SnGeneratorUtil.getId());
                    billingDetail.setCreateTime(now);
                }
            } else {
                // 没有ID，新增记录
                billingDetail = new BillingDetail();
                BeanUtils.copyProperties(item, billingDetail);
                billingDetail.setId(SnGeneratorUtil.getId());
                billingDetail.setCreateTime(now);
            }

            // 设置关联的账单信息ID
            billingDetail.setBillingInfoId(billingInfoId);

            // 如果费用等级为空，且费用类别和项目名称不为空，调用三目录相似搜索
	        threeCatalogueSearchUtils.setFeeLevelAndThreeCatalogueIdBySearch(billingDetail);

            // 自动计算金额、数量、单价
            billingDetail.autoCalculate();

            // 处理orderNum，如果未提供序号且是新增，则根据已有明细数量和当前索引自动设置
            if (billingDetail.getOrderNum() == null && !isUpdate) {
                billingDetail.setOrderNum((int)existingDetailsCount + newItemIndex + 1);
                newItemIndex++;
            }

            // 设置更新时间
            billingDetail.setUpdateTime(now);

            // 保存到数据库
            mongoTemplate.save(billingDetail);
            savedDetails.add(billingDetail);
        }

        return CommonResult.successData(savedDetails);
    }

    /**
     * 更新账单明细
     *
     * @param billingDetail 账单明细
     * @return 更新结果
     */
    @PostMapping("/update")
    public CommonResult<BillingDetail> update(@RequestBody BillingDetail billingDetail) {
        // 检查ID是否存在
        if (billingDetail.getId() == null) {
            return CommonResult.failResult(10001, "账单明细ID不能为空");
        }

        // 查询原有数据
        BillingDetail existingDetail = mongoTemplate.findById(billingDetail.getId(), BillingDetail.class);
        if (existingDetail == null) {
            return CommonResult.failResult(10001, "账单明细不存在");
        }

        // 保留关联的账单信息ID
        if (billingDetail.getBillingInfoId() == null) {
            billingDetail.setBillingInfoId(existingDetail.getBillingInfoId());
        }

        // 检查费用类别和项目名称是否发生变化，如果变化则清空三目录ID
        boolean feeTypeChanged = !Objects.equals(existingDetail.getFeeType(), billingDetail.getFeeType());
        boolean projectNameChanged = !Objects.equals(existingDetail.getProjectName(), billingDetail.getProjectName());

        if (feeTypeChanged || projectNameChanged) {
            log.info("费用类别或项目名称发生变化，清空三目录ID，电子清单ID：{}，项目名称：{} -> {}，费用类别：{} -> {}",
                billingDetail.getId(), existingDetail.getProjectName(), billingDetail.getProjectName(),
                existingDetail.getFeeType(), billingDetail.getFeeType());
            billingDetail.setThreeCatalogueId(null);
            billingDetail.setFeeLevel(null); // 同时清空费用等级，让其重新匹配
        }

        // 如果费用等级为空，且费用类别和项目名称不为空，调用三目录相似搜索
        threeCatalogueSearchUtils.setFeeLevelAndThreeCatalogueIdBySearch(billingDetail);

        // 设置更新时间，保留创建时间
        billingDetail.setUpdateTime(new Date());
        billingDetail.setCreateTime(existingDetail.getCreateTime());

	    // 自动计算金额、数量、单价
	    billingDetail.autoCalculate();

        // 更新到数据库
        mongoTemplate.save(billingDetail);

        return CommonResult.successData(billingDetail);
    }

    /**
     * 批量删除账单明细
     *
     * @param batchDeleteIn 批量删除参数
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public CommonResult<Long> batchDelete(@RequestBody BillingDetailBatchDeleteIn batchDeleteIn) {
        // 检查ID列表是否为空
        List<Long> ids = batchDeleteIn.getIds();
        if (ids == null || ids.isEmpty()) {
            return CommonResult.failResult(10001, "账单明细ID列表不能为空");
        }

        // 执行删除
        Query deleteQuery = Query.query(Criteria.where("_id").in(ids));
        DeleteResult result = mongoTemplate.remove(deleteQuery, BillingDetail.class);

        return CommonResult.successData(result.getDeletedCount());
    }

    /**
     * 根据账单信息ID查询关联的账单明细列表
     *
     * @param billingInfoId 账单信息ID
     * @return 账单明细列表
     */
    @GetMapping("/listByBillingInfo")
    public CommonResult<List<BillingDetail>> listByBillingInfo(@RequestParam Long billingInfoId) {
        // 验证账单信息是否存在
        BillingInfo billingInfo = mongoTemplate.findById(billingInfoId, BillingInfo.class);
        if (billingInfo == null) {
            return CommonResult.failResult(10001, "账单信息不存在");
        }

        // 查询关联的账单明细
        Query query = Query.query(Criteria.where("billingInfoId").is(billingInfoId));
        query.with(Sort.by(Sort.Direction.ASC, "orderNum").and(Sort.by(Sort.Direction.DESC, "createTime")));

        List<BillingDetail> billingDetails = mongoTemplate.find(query, BillingDetail.class);

        return CommonResult.successData(billingDetails);
    }

    /**
     * 批量修改账单明细的不可报销金额
     *
     * @param updateRequest 批量修改请求参数
     * @return 操作结果
     */
    @PostMapping("/batchUpdateReimbursable")
    public CommonResult<String> batchUpdateReimbursable(@RequestBody BillingDetailBatchUpdateReimbursableIn updateRequest) {
        // 参数验证
        if (updateRequest.getBillingDetailIds() == null || updateRequest.getBillingDetailIds().isEmpty()) {
            return CommonResult.failResult(10001, "账单明细ID列表不能为空");
        }
        if (updateRequest.getOperationType() == null) {
            return CommonResult.failResult(10001, "操作类型不能为空");
        }

        // 验证操作类型的合法性
        Integer operationType = updateRequest.getOperationType();
        if (operationType < 0 || operationType > 2) {
            return CommonResult.failResult(10001, "操作类型必须为0（可报销）、1（不可报销）或2（不可报销-非工伤）");
        }

        // 查询要修改的账单明细
        Query query = Query.query(Criteria.where("id").in(updateRequest.getBillingDetailIds()));
        List<BillingDetail> billingDetails = mongoTemplate.find(query, BillingDetail.class);

        if (billingDetails.isEmpty()) {
            return CommonResult.failResult(10001, "未找到要修改的账单明细");
        }

        // 批量修改
        int updatedCount = 0;
        for (BillingDetail billingDetail : billingDetails) {
            try {
                updateBillingDetailReimbursable(billingDetail, operationType);
                mongoTemplate.save(billingDetail);
                updatedCount++;
            } catch (Exception e) {
                log.error("修改账单明细不可报销金额异常，明细ID：{}，项目名称：{}",
                    billingDetail.getId(), billingDetail.getProjectName(), e);
            }
        }

        log.info("批量修改账单明细不可报销金额完成，操作类型：{}，总数：{}，成功数：{}",
            operationType, billingDetails.size(), updatedCount);

        return CommonResult.successResult(String.format("批量修改完成，总数：%d，成功数：%d",
            billingDetails.size(), updatedCount));
    }

    /**
     * 根据操作类型修改单个账单明细的不可报销金额
     *
     * @param billingDetail 账单明细
     * @param operationType 操作类型（0-可报销，1-不可报销，2-不可报销非工伤）
     */
    private void updateBillingDetailReimbursable(BillingDetail billingDetail, Integer operationType) {
        switch (operationType) {
            case 0: // 可报销
                billingDetail.setNonReimbursableAmount(BigDecimal.ZERO);
                billingDetail.setDeductionType(null);
                break;

            case 1: // 不可报销
                if (billingDetail.getAmount() != null) {
                    billingDetail.setNonReimbursableAmount(billingDetail.getAmount());
                }
                billingDetail.setDeductionType("审核扣减");
                break;

            case 2: // 不可报销（非工伤）
                if (billingDetail.getAmount() != null) {
                    billingDetail.setNonReimbursableAmount(billingDetail.getAmount());
                }
                billingDetail.setDeductionType("非工伤扣减");
                billingDetail.setIsWorkInjury(false);
                break;

            default:
                throw new IllegalArgumentException("不支持的操作类型：" + operationType);
        }

        // 设置更新时间
        billingDetail.setUpdateTime(new Date());
    }

    /**
     * 电子清单匹配三目录
     * 更新电子清单的三目录关联信息，并基于三目录信息重新计算费用
     *
     * @param matchRequest 匹配请求参数
     * @return 更新后的电子清单信息
     */
    @PostMapping("/matchThreeCatalogue")
    public CommonResult<BillingDetail> matchThreeCatalogue(@RequestBody BillingDetailMatchThreeCatalogueIn matchRequest) {
        // 参数验证
        if (matchRequest.getBillingDetailId() == null) {
            return CommonResult.failResult(10001, "电子清单ID不能为空");
        }
        if (matchRequest.getThreeCatalogueId() == null) {
            return CommonResult.failResult(10001, "三目录ID不能为空");
        }

        // 查询电子清单信息
        BillingDetail billingDetail = mongoTemplate.findById(matchRequest.getBillingDetailId(), BillingDetail.class);
        if (billingDetail == null) {
            return CommonResult.failResult(10001, "电子清单不存在");
        }

        // 查询三目录信息
        ThreeCatalogue threeCatalogue = mongoTemplate.findById(matchRequest.getThreeCatalogueId(), ThreeCatalogue.class);
        if (threeCatalogue == null) {
            return CommonResult.failResult(10001, "三目录不存在");
        }

        // 更新电子清单的三目录关联信息
        billingDetail.setThreeCatalogueId(matchRequest.getThreeCatalogueId().toString());

        // 基于三目录信息更新费用相关字段
        updateBillingDetailFromThreeCatalogue(billingDetail, threeCatalogue);

        // 重新计算费用（调用autoCalculate方法）
        billingDetail.autoCalculate();

        // 设置更新时间
        billingDetail.setUpdateTime(new Date());

        // 保存到数据库
        mongoTemplate.save(billingDetail);

        log.info("电子清单匹配三目录成功，电子清单ID：{}，三目录ID：{}，项目名称：{}",
            matchRequest.getBillingDetailId(), matchRequest.getThreeCatalogueId(), threeCatalogue.getProjectName());

        return CommonResult.successData(billingDetail);
    }

    /**
     * 基于三目录信息更新电子清单的费用相关字段
     *
     * @param billingDetail 电子清单
     * @param threeCatalogue 三目录
     */
    private void updateBillingDetailFromThreeCatalogue(BillingDetail billingDetail, ThreeCatalogue threeCatalogue) {
        // 更新费用等级
        billingDetail.setFeeLevel(threeCatalogue.getLevel());

        // 根据三目录的目录类别推导对应的电子清单费用类别（如果电子清单的费用类别为空）
        if (StrUtil.isBlank(billingDetail.getFeeType())) {
            String feeType = convertThreeCatalogueTypeToFeeType(threeCatalogue.getType());
            if (StrUtil.isNotBlank(feeType)) {
                billingDetail.setFeeType(feeType);
            }
        }

        // 如果项目名称为空，使用三目录的项目名称
        if (StrUtil.isBlank(billingDetail.getProjectName())) {
            billingDetail.setProjectName(threeCatalogue.getProjectName());
        }

        log.debug("基于三目录更新电子清单字段，费用等级：{}，三目录类别：{}，推导费用类别：{}",
            threeCatalogue.getLevel(), threeCatalogue.getType(), billingDetail.getFeeType());
    }

    /**
     * 将三目录的目录类别转换为电子清单对应的费用类别
     * 这是ThreeCatalogueSearchUtils.convertFeeTypeToThreeCatalogueTypes的反向转换
     *
     * @param threeCatalogueType 三目录的目录类别
     * @return 电子清单对应的费用类别
     */
    private String convertThreeCatalogueTypeToFeeType(String threeCatalogueType) {
        if (StrUtil.isBlank(threeCatalogueType)) {
            return null;
        }

        switch (threeCatalogueType) {
            case "药品目录":
                return "药品费";
            case "诊疗服务":
                // 诊疗服务对应多种费用类别，这里返回最常见的治疗费
                // 具体的费用类别可能需要根据项目名称进一步判断
                return "治疗费";
            case "医用耗材":
                return "材料费";
            default:
                // 如果没有匹配的转换规则，返回null，保持原有费用类别不变
                log.debug("未找到三目录类别对应的费用类别，三目录类别：{}", threeCatalogueType);
                return null;
        }
    }

}
