package com.yixun.wid.v2.bean.in;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 修改账单限价医院等级请求参数
 */
@Data
public class UpdateBillingPriceLimitLevelIn {

    /**
     * 账单ID
     */
    @NotNull(message = "账单ID不能为空")
    private Long billingInfoId;

    /**
     * 医疗机构ID（可选参数）
     * 当不为空时，会同时更新医疗机构的限价等级
     */
    private Long medicalInstitutionId;

    /**
     * 新的限价医院等级
     */
    @NotNull(message = "限价医院等级不能为空")
    private String priceLimitLevel;
}
