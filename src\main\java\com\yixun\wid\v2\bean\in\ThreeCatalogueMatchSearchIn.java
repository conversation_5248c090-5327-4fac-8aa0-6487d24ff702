package com.yixun.wid.v2.bean.in;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 三目录匹配查询输入参数
 */
@Data
public class ThreeCatalogueMatchSearchIn {
    
    /**
     * 项目名称（模糊搜索）
     */
    private String projectName;
    
    /**
     * 目录类别
     */
    private String type;
    
    /**
     * 目录编码（模糊搜索）
     */
    private String sn;
    
    /**
     * 限价医院等级（特殊参数，用于查询对应的限价信息）
     */
    private String priceLimitLevel;

    /**
     * 电子清单id（必填参数，用于查询对应的电子清单关联的三目录信息和账单时间）
     */
    @NotNull(message = "电子清单ID不能为空")
    private Long billingDetailId;
}
