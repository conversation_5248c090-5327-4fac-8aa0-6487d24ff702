package com.yixun.wid.v2.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yixun.wid.v2.entity.MedicalInstitutions;
import com.yixun.wid.v2.service.MedicalInstitutionsService;
import com.yixun.wid.v2.vo.UpdatePriceLimitLevelIn;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 医疗机构服务实现类
 */
@Slf4j
@Service
public class MedicalInstitutionsServiceImpl implements MedicalInstitutionsService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void updatePriceLimitLevel(UpdatePriceLimitLevelIn updatePriceLimitLevelIn) {
        Long hospitalId = updatePriceLimitLevelIn.getHospitalId();
        String priceLimitLevel = updatePriceLimitLevelIn.getPriceLimitLevel();

        // 查询医院信息
        MedicalInstitutions hospital = mongoTemplate.findById(hospitalId, MedicalInstitutions.class);
        if (ObjectUtil.isNull(hospital)) {
            throw new RuntimeException("医院不存在");
        }

        // 检查限价医院等级是否为空，如果已有值则不更新
        if (StrUtil.isNotBlank(hospital.getPriceLimitLevel())) {
            log.info("医院已设置限价医院等级，跳过更新，医院ID：{}，当前限价医院等级：{}", hospitalId, hospital.getPriceLimitLevel());
        } else {
            // 更新限价医院等级
            hospital.setPriceLimitLevel(priceLimitLevel.trim());
            hospital.setUpdateTime(new Date());

            // 保存到数据库
            mongoTemplate.save(hospital);

            log.info("更新医院限价医院等级成功，医院ID：{}，限价医院等级：{}", hospitalId, priceLimitLevel);
        }
    }
}
