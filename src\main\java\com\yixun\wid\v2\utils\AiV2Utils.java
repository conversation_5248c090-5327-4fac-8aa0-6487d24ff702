package com.yixun.wid.v2.utils;

import cn.hutool.http.HttpUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yixun.wid.exception.DataErrorException;
import com.yixun.wid.v2.vo.AiConclusionRequestVO;
import com.yixun.wid.v2.vo.AiConclusionResponseVO;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.v2.vo.MaterialAuditInternalRequestVO;
import com.yixun.wid.v2.vo.MaterialAuditResponseVO;
import com.yixun.wid.v2.vo.MaterialRecognitionResult;
import com.yixun.wid.v2.vo.RecommendedCase;
import com.yixun.wid.v2.vo.SimilarAiCheckOut;
import com.yixun.wid.v2.vo.SimilarAiCheckResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AiV2Utils {

    @Value("${api.ai.conclusionRecommendUrl:}")
    private String conclusionRecommendUrl;

    @Value("${api.ai.materialRecognitionUrl:}")
    private String materialRecognitionUrl;

    @Value("${api.ai.materialRecognitionProgressUrl:}")
    private String materialRecognitionProgressUrl;

    @Value("${api.ai.materialAuditUrl:}")
    private String materialAuditUrl;

    @Value("${api.ai.caseRecommendationUrl:}")
    private String caseRecommendationUrl;

    @Value("${api.ai.quickFillUrl:}")
    private String quickFillUrl;

    private static final String SECRET_HEADER = "SDx78cfbjn";

    /**
     * 获取结论审核推荐
     * 调用AI接口获取结论审核推荐
     *
     * @param requestVO AI请求实体
     * @return AI响应实体
     */
    public AiConclusionResponseVO getConclusionRecommendation(AiConclusionRequestVO requestVO) {
        log.info("调用AI结论审核推荐，事故经过：{}", requestVO.getAccidentDetail());

        // 检查配置
        if (StrUtil.isBlank(conclusionRecommendUrl)) {
            log.warn("AI结论审核推荐接口URL未配置，返回空值");
            return null;
        }

        try {
            // 构建请求参数
            JSONObject requestJson = new JSONObject();
            requestJson.put("accidentDetail", requestVO.getAccidentDetail());
            requestJson.put("legalRegulations", requestVO.getLegalRegulations());

            // 发送HTTP请求
            String response = HttpUtil.createPost(conclusionRecommendUrl)
                    .header("Content-Type", "application/json")
                    .header("Authorization", SECRET_HEADER)
                    .body(requestJson.toJSONString())
                    .timeout(30000) // 30秒超时
                    .execute()
                    .body();

            log.info("AI结论审核推荐接口响应：{}", response);

            // 解析响应
            if (StrUtil.isNotBlank(response)) {
                JSONObject responseJson = JSON.parseObject(response);

                // 检查响应状态
                if (responseJson.containsKey("success") && responseJson.getBoolean("success")) {
                    JSONObject data = responseJson.getJSONObject("data");
                    if (data != null) {
                        AiConclusionResponseVO responseVO = JSON.parseObject(data.toJSONString(), AiConclusionResponseVO.class);
                        log.info("AI结论审核推荐完成，结论：{}", responseVO.getConclusion());
                        return responseVO;
                    }
                } else {
                    String errorMsg = responseJson.getString("message");
                    log.error("AI结论审核推荐接口返回错误：{}", errorMsg);
                    throw new DataErrorException("AI接口调用失败：" + errorMsg);
                }
            }

            log.error("AI结论审核推荐接口返回空响应");
            throw new DataErrorException("AI接口返回空响应");

        } catch (Exception e) {
            log.error("调用AI结论审核推荐接口异常", e);
            // 发生异常时返回空值
            log.warn("AI接口调用失败，返回空值");
            return null;
        }
    }



    /**
     * 检查相似企业
     * 模拟实现，返回示例数据
     *
     * @param name 企业名称
     * @param num 返回数量
     * @return 相似企业检查结果
     */
    public SimilarAiCheckOut checkSimilar(String name, Integer num) {
        log.info("调用AI相似企业检查，企业名称：{}，返回数量：{}", name, num);

        // 模拟返回数据
        SimilarAiCheckOut result = new SimilarAiCheckOut();
        result.setSearchName(name);
        result.setProcessName(name);

        // 模拟相似企业列表
        List<SimilarAiCheckResult> similarResults = new ArrayList<>();
        for (int i = 1; i <= Math.min(num, 3); i++) {
            SimilarAiCheckResult similarResult = new SimilarAiCheckResult();
            similarResult.setRawName(name + "相似企业" + i);
            similarResult.setScore("0." + (90 - i * 5)); // 模拟相似度分数
            similarResult.setProcessName(name + "企业" + i);
            similarResult.setCorpData("模拟企业数据" + i);
            similarResults.add(similarResult);
        }

        result.setResult(similarResults);

        log.info("AI相似企业检查完成，返回{}个相似企业", similarResults.size());
        return result;
    }

    /**
     * AI材料类型识别
     * 调用AI接口识别材料类型
     *
     * @param taskId 任务ID
     * @param fileUrls 需要识别的文件URL列表
     * @param materialTypeDict 材料类型字典列表（用于AI识别参考）
     * @return 识别结果，key为材料类型，value为识别结果列表
     */
    public Map<String, List<MaterialRecognitionResult>> recognizeMaterialTypes(Long taskId, List<String> fileUrls, List<String> materialTypeDict) {
        log.info("调用AI材料类型识别，任务ID：{}，文件数量：{}，字典类型数量：{}", taskId, fileUrls.size(), materialTypeDict.size());

        // 检查配置
        if (StrUtil.isBlank(materialRecognitionUrl)) {
            log.warn("AI材料类型识别接口URL未配置，返回空值");
            return null;
        }

        try {
            // 构建请求参数
            JSONObject requestJson = new JSONObject();
            requestJson.put("taskId", taskId);
            requestJson.put("fileUrls", fileUrls);
            requestJson.put("materialTypeDict", materialTypeDict);

            // 发送HTTP请求
            String response = HttpUtil.createPost(materialRecognitionUrl)
                    .header("Content-Type", "application/json")
                    .header("Authorization", SECRET_HEADER)
                    .body(requestJson.toJSONString())
                    .timeout(30000) // 30秒超时
                    .execute()
                    .body();

            log.info("AI材料类型识别接口响应：{}", response);

            // 解析响应
            if (StrUtil.isNotBlank(response)) {
                JSONObject responseJson = JSON.parseObject(response);

                // 检查响应状态
                if (responseJson.containsKey("success") && responseJson.getBoolean("success")) {
                    JSONObject data = responseJson.getJSONObject("data");
                    if (data != null) {
                        Map<String, List<MaterialRecognitionResult>> result =
                            JSON.parseObject(data.toJSONString(), Map.class);
                        log.info("AI材料类型识别完成，识别到{}种材料类型", result.size());
                        return result;
                    }
                } else {
                    String errorMsg = responseJson.getString("message");
                    log.error("AI材料类型识别接口返回错误：{}", errorMsg);
                    throw new DataErrorException("AI接口调用失败：" + errorMsg);
                }
            }

            log.error("AI材料类型识别接口返回空响应");
            throw new DataErrorException("AI接口返回空响应");

        } catch (Exception e) {
            log.error("调用AI材料类型识别接口异常", e);
            // 发生异常时返回空值
            log.warn("AI接口调用失败，返回空值");
            return null;
        }
    }

    /**
     * 查询材料类型识别进度
     * 根据任务ID查询已处理的材料数量
     *
     * @param taskId 任务ID
     * @return 已处理的材料数量，如果查询失败返回null
     */
    public Integer getMaterialRecognitionProgress(Long taskId) {
        log.info("查询材料类型识别进度，任务ID：{}", taskId);

        // 检查配置
        if (StrUtil.isBlank(materialRecognitionProgressUrl)) {
            log.warn("AI材料类型识别进度查询接口URL未配置，返回空值");
            return null;
        }

        try {
            // 构建请求参数
            JSONObject requestJson = new JSONObject();
            requestJson.put("taskId", taskId);

            // 发送HTTP请求
            String response = HttpUtil.createPost(materialRecognitionProgressUrl)
                    .header("Content-Type", "application/json")
                    .header("Authorization", SECRET_HEADER)
                    .body(requestJson.toJSONString())
                    .timeout(30000) // 30秒超时
                    .execute()
                    .body();

            log.info("AI材料类型识别进度查询接口响应：{}", response);

            // 解析响应
            if (StrUtil.isNotBlank(response)) {
                JSONObject responseJson = JSON.parseObject(response);

                // 检查响应状态
                if (responseJson.containsKey("success") && responseJson.getBoolean("success")) {
                    JSONObject data = responseJson.getJSONObject("data");
                    if (data != null && data.containsKey("processedCount")) {
                        Integer processedCount = data.getInteger("processedCount");
                        log.info("材料类型识别进度查询完成，已处理材料数量：{}", processedCount);
                        return processedCount;
                    }
                } else {
                    String errorMsg = responseJson.getString("message");
                    log.error("AI材料类型识别进度查询接口返回错误：{}", errorMsg);
                    throw new DataErrorException("AI接口调用失败：" + errorMsg);
                }
            }

            log.error("AI材料类型识别进度查询接口返回空响应");
            throw new DataErrorException("AI接口返回空响应");

        } catch (Exception e) {
            log.error("调用AI材料类型识别进度查询接口异常", e);
            // 发生异常时返回空值
            log.warn("AI接口调用失败，返回空值");
            return null;
        }
    }

    /**
     * AI材料审核
     * 调用AI接口进行材料审核
     *
     * @param requestVO 材料审核内部请求参数
     * @return 材料审核结果，如果调用失败返回null
     */
    public MaterialAuditResponseVO auditMaterials(MaterialAuditInternalRequestVO requestVO) {
        log.info("调用AI材料审核，类型：{}", requestVO.getType());

        // 检查配置
        if (StrUtil.isBlank(materialAuditUrl)) {
            log.warn("AI材料审核接口URL未配置，返回空值");
            return null;
        }

        try {
            // 构建请求参数
            JSONObject requestJson = new JSONObject();
            requestJson.put("type", requestVO.getType());

            // 根据类型添加相应的数据
            if ("declaration".equals(requestVO.getType())) {
                requestJson.put("declaration", requestVO.getDeclaration());
            } else if ("cases".equals(requestVO.getType())) {
                requestJson.put("cases", requestVO.getCases());
            }

            // 发送HTTP请求
            String response = HttpUtil.createPost(materialAuditUrl)
                    .header("Content-Type", "application/json")
                    .header("Authorization", SECRET_HEADER)
                    .body(requestJson.toJSONString())
                    .timeout(30000) // 30秒超时
                    .execute()
                    .body();

            log.info("AI材料审核接口响应：{}", response);

            // 解析响应
            if (StrUtil.isNotBlank(response)) {
                JSONObject responseJson = JSON.parseObject(response);

                // 检查响应状态
                if (responseJson.containsKey("success") && responseJson.getBoolean("success")) {
                    JSONObject data = responseJson.getJSONObject("data");
                    if (data != null) {
                        MaterialAuditResponseVO responseVO = JSON.parseObject(data.toJSONString(), MaterialAuditResponseVO.class);
                        log.info("AI材料审核完成，审核建议：{}", responseVO.getAuditSuggestion());
                        return responseVO;
                    }
                } else {
                    String errorMsg = responseJson.getString("message");
                    log.error("AI材料审核接口返回错误：{}", errorMsg);
                    throw new DataErrorException("AI接口调用失败：" + errorMsg);
                }
            }

            log.error("AI材料审核接口返回空响应");
            throw new DataErrorException("AI接口返回空响应");

        } catch (Exception e) {
            log.error("调用AI材料审核接口异常", e);
            // 发生异常时返回空值
            log.warn("AI接口调用失败，返回空值");
            return null;
        }
    }

    /**
     * AI案例推荐
     * 根据事故经过推荐相关案例
     *
     * @param accidentDetail 事故经过
     * @return 推荐案例列表，如果调用失败返回null
     */
    public List<RecommendedCase> recommendCases(String accidentDetail) {
        log.info("调用AI案例推荐，事故经过长度：{}", accidentDetail != null ? accidentDetail.length() : 0);

        // 检查配置
        if (StrUtil.isBlank(caseRecommendationUrl)) {
            log.warn("AI案例推荐接口URL未配置，返回空值");
            return null;
        }

        try {
            // 构建请求参数
            JSONObject requestJson = new JSONObject();
            requestJson.put("accidentDetail", accidentDetail);

            // 发送HTTP请求
            String response = HttpUtil.createPost(caseRecommendationUrl)
                    .header("Content-Type", "application/json")
                    .header("Authorization", SECRET_HEADER)
                    .body(requestJson.toJSONString())
                    .timeout(30000) // 30秒超时
                    .execute()
                    .body();

            log.info("AI案例推荐接口响应：{}", response);

            // 解析响应
            if (StrUtil.isNotBlank(response)) {
                JSONObject responseJson = JSON.parseObject(response);

                // 检查响应状态
                if (responseJson.containsKey("success") && responseJson.getBoolean("success")) {
                    JSONObject data = responseJson.getJSONObject("data");
                    if (data != null && data.containsKey("recommendedCases")) {
                        List<RecommendedCase> recommendedCases = JSON.parseArray(
                            data.getJSONArray("recommendedCases").toJSONString(),
                            RecommendedCase.class
                        );
                        log.info("AI案例推荐完成，推荐案例数量：{}", recommendedCases.size());
                        return recommendedCases;
                    }
                } else {
                    String errorMsg = responseJson.getString("message");
                    log.error("AI案例推荐接口返回错误：{}", errorMsg);
                    throw new DataErrorException("AI接口调用失败：" + errorMsg);
                }
            }

            log.error("AI案例推荐接口返回空响应");
            throw new DataErrorException("AI接口返回空响应");

        } catch (Exception e) {
            log.error("调用AI案例推荐接口异常", e);
            // 发生异常时返回空值
            log.warn("AI接口调用失败，返回空值");
            return null;
        }
    }

    /**
     * AI快捷填充
     * 根据材料识别结果智能填充Declaration对象
     *
     * @param materialRecognitionResults 材料识别结果
     * @return 填充后的Declaration对象，如果调用失败返回null
     */
    public Declaration quickFillDeclaration(Map<String, List<MaterialRecognitionResult>> materialRecognitionResults) {
        log.info("调用AI快捷填充，材料类型数量：{}", materialRecognitionResults.size());

        // 检查配置
        if (StrUtil.isBlank(quickFillUrl)) {
            log.warn("AI快捷填充接口URL未配置，返回空值");
            return null;
        }

        try {
            // 构建请求参数
            JSONObject requestJson = new JSONObject();
            requestJson.put("materialRecognitionResults", materialRecognitionResults);

            // 发送HTTP请求
            String response = HttpUtil.createPost(quickFillUrl)
                    .header("Content-Type", "application/json")
                    .header("Authorization", SECRET_HEADER)
                    .body(requestJson.toJSONString())
                    .timeout(30000) // 30秒超时
                    .execute()
                    .body();

            log.info("AI快捷填充接口响应：{}", response);

            // 解析响应
            if (StrUtil.isNotBlank(response)) {
                JSONObject responseJson = JSON.parseObject(response);

                // 检查响应状态
                if (responseJson.containsKey("success") && responseJson.getBoolean("success")) {
                    JSONObject data = responseJson.getJSONObject("data");
                    if (data != null && data.containsKey("declaration")) {
                        Declaration declaration = JSON.parseObject(
                            data.getJSONObject("declaration").toJSONString(),
                            Declaration.class
                        );
                        log.info("AI快捷填充完成，填充了Declaration对象");
                        return declaration;
                    }
                } else {
                    String errorMsg = responseJson.getString("message");
                    log.error("AI快捷填充接口返回错误：{}", errorMsg);
                    throw new DataErrorException("AI接口调用失败：" + errorMsg);
                }
            }

            log.error("AI快捷填充接口返回空响应");
            throw new DataErrorException("AI接口返回空响应");

        } catch (Exception e) {
            log.error("调用AI快捷填充接口异常", e);
            // 发生异常时返回空值
            log.warn("AI接口调用失败，返回空值");
            return null;
        }
    }
}
