package com.yixun.wid.v2.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 检查医院性质响应结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckHospitalNatureResponse {
    
    /**
     * 医院性质
     * 可能的值：
     * - "其他" - 未查询到医疗机构信息
     * - "定点医院（禁用）" - 医疗机构状态为禁用
     * - "非定点医院" - 识别为非协议机构
     * - "定点医院" - 在有效期内的定点医院
     * - "定点医院（未开始）" - 门诊时间在开始日期之前
     * - "定点医院（已终止）" - 门诊时间在结束日期之后
     */
    private String hospitalNature;
}
