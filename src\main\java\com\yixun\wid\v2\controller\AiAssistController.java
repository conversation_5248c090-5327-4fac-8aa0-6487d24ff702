package com.yixun.wid.v2.controller;

import cn.hutool.core.util.ObjectUtil;
import com.yixun.bean.CommonResult;
import com.yixun.wid.entity.Cases;
import com.yixun.wid.entity.Declaration;
import com.yixun.wid.entity.em.DictType;
import com.yixun.wid.v2.entity.LegalClause;
import com.yixun.wid.v2.utils.AiV2Utils;
import com.yixun.wid.v2.entity.DataDict;
import com.yixun.wid.v2.service.DataDictService;
import com.yixun.wid.v2.vo.AiConclusionRequestVO;
import com.yixun.wid.v2.vo.AiConclusionResponseVO;
import com.yixun.wid.v2.vo.AiQuickFillRequestVO;
import com.yixun.wid.v2.vo.CaseRecommendationRequestVO;
import com.yixun.wid.v2.vo.MaterialAuditInternalRequestVO;
import com.yixun.wid.v2.vo.MaterialAuditRequestVO;
import com.yixun.wid.v2.vo.MaterialAuditResponseVO;
import com.yixun.wid.v2.vo.MaterialRecognitionRequestVO;
import com.yixun.wid.v2.vo.MaterialRecognitionResult;
import com.yixun.wid.v2.vo.RecommendedCase;
import lombok.AllArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI辅助审核Controller
 */
@RestController
@RequestMapping("/v2/ai/assist")
@AllArgsConstructor
public class AiAssistController {

    private final MongoTemplate mongoTemplate;
    private final AiV2Utils aiV2Utils;
    private final LegalClauseController legalClauseController;
    private final DataDictService dataDictService;

    /**
     * 获取结论审核推荐
     *
     * @param caseId        案件ID（可选）
     * @param declarationId 申报ID（可选）
     * @return AI结论审核推荐结果
     */
    @GetMapping("/conclusion/recommend")
    public CommonResult<AiConclusionResponseVO> getConclusionRecommendation(
            @RequestParam(required = false) Long caseId,
            @RequestParam(required = false) Long declarationId) {

        // 参数验证：确保caseId和declarationId有且仅有一个不为空
        if ((ObjectUtil.isNull(caseId) && ObjectUtil.isNull(declarationId)) ||
            (ObjectUtil.isNotNull(caseId) && ObjectUtil.isNotNull(declarationId))) {
            throw new RuntimeException("caseId和declarationId必须且只能传递其中一个参数");
        }

        // 获取事故经过
        String accidentDetail = getAccidentDetail(caseId, declarationId);

        // 获取所有法规条款数据
        CommonResult<List<LegalClause>> legalClausesResult =
            legalClauseController.getAllClauses();

        if (ObjectUtil.isNull(legalClausesResult) || ObjectUtil.isNull(legalClausesResult.getData())) {
            throw new RuntimeException("获取法规条款数据失败");
        }

        // 封装AI请求实体
        AiConclusionRequestVO requestVO = new AiConclusionRequestVO();
        requestVO.setAccidentDetail(accidentDetail);
        requestVO.setLegalRegulations(legalClausesResult.getData());

        // 调用AI工具类获取推荐结果
        AiConclusionResponseVO responseVO = aiV2Utils.getConclusionRecommendation(requestVO);

        return CommonResult.successData(responseVO);
    }

    /**
     * AI材料类型识别
     *
     * @param requestVO 材料识别请求参数
     * @return 材料类型识别结果
     */
    @PostMapping("/material/recognize")
    public CommonResult<Map<String, List<MaterialRecognitionResult>>> recognizeMaterialTypes(
            @Valid @RequestBody MaterialRecognitionRequestVO requestVO) {

        // 查询declarationMaterials字典数据
        List<DataDict> dataDictList = dataDictService.getAllDataDictList(DictType.declarationMaterials.name());
        if (ObjectUtil.isEmpty(dataDictList)) {
            throw new RuntimeException("未找到declarationMaterials字典数据");
        }

        // 提取字典标签作为材料类型字典
        List<String> materialTypeDict = dataDictList.stream()
                .map(DataDict::getLabel)
                .collect(Collectors.toList());

        // 调用AI工具类进行材料类型识别
        Map<String, List<MaterialRecognitionResult>> result =
            aiV2Utils.recognizeMaterialTypes(requestVO.getTaskId(), requestVO.getFileUrls(), materialTypeDict);

        return CommonResult.successData(result);
    }

    /**
     * 查询工伤申报材料类型识别进度
     *
     * @param taskId 任务ID
     * @return 已处理的材料数量
     */
    @GetMapping("/material/recognize/progress")
    public CommonResult<Integer> getMaterialRecognitionProgress(@RequestParam("taskId") Long taskId) {

        // 调用AI工具类查询识别进度
        Integer processedCount = aiV2Utils.getMaterialRecognitionProgress(taskId);

        return CommonResult.successData(processedCount);
    }

    /**
     * AI材料审核
     *
     * @param requestVO 材料审核请求参数
     * @return 材料审核结果
     */
    @PostMapping("/material/audit")
    public CommonResult<MaterialAuditResponseVO> auditMaterials(
            @Valid @RequestBody MaterialAuditRequestVO requestVO) {

        MaterialAuditInternalRequestVO internalRequest;

        // 根据类型查询相应的数据并构建内部请求对象
        if ("declaration".equals(requestVO.getType())) {
            Declaration declaration = mongoTemplate.findById(requestVO.getId(), Declaration.class);
            if (ObjectUtil.isNull(declaration)) {
                throw new RuntimeException("申报记录不存在，ID：" + requestVO.getId());
            }
            internalRequest = MaterialAuditInternalRequestVO.forDeclaration(declaration);
        } else if ("cases".equals(requestVO.getType())) {
            Cases cases = mongoTemplate.findById(requestVO.getId(), Cases.class);
            if (ObjectUtil.isNull(cases)) {
                throw new RuntimeException("案件记录不存在，ID：" + requestVO.getId());
            }
            internalRequest = MaterialAuditInternalRequestVO.forCases(cases);
        } else {
            throw new RuntimeException("不支持的类型：" + requestVO.getType());
        }

        // 调用AI工具类进行材料审核
        MaterialAuditResponseVO result = aiV2Utils.auditMaterials(internalRequest);

        return CommonResult.successData(result);
    }

    /**
     * AI案例推荐
     *
     * @param requestVO 案例推荐请求参数
     * @return 推荐案例列表
     */
    @PostMapping("/case/recommend")
    public CommonResult<List<RecommendedCase>> recommendCases(
            @Valid @RequestBody CaseRecommendationRequestVO requestVO) {

        // 调用AI工具类进行案例推荐
        List<RecommendedCase> result = aiV2Utils.recommendCases(requestVO.getAccidentDetail());

        return CommonResult.successData(result);
    }

    /**
     * AI快捷填充
     *
     * @param requestVO AI快捷填充请求参数
     * @return 填充后的Declaration对象
     */
    @PostMapping("/quick-fill")
    public CommonResult<Declaration> quickFillDeclaration(
            @Valid @RequestBody AiQuickFillRequestVO requestVO) {

        // 调用AI工具类进行快捷填充
        Declaration result = aiV2Utils.quickFillDeclaration(requestVO.getMaterialRecognitionResults());

        return CommonResult.successData(result);
    }

    /**
     * 获取事故经过
     *
     * @param caseId        案件ID
     * @param declarationId 申报ID
     * @return 事故经过
     */
    private String getAccidentDetail(Long caseId, Long declarationId) {
        if (ObjectUtil.isNotNull(caseId)) {
            // 从Cases表查询
            Cases cases = mongoTemplate.findById(caseId, Cases.class);
            if (ObjectUtil.isNull(cases)) {
                throw new RuntimeException("案件记录不存在");
            }
            return cases.getAccidentDetail();
        } else {
            // 从Declaration表查询
            Declaration declaration = mongoTemplate.findById(declarationId, Declaration.class);
            if (ObjectUtil.isNull(declaration)) {
                throw new RuntimeException("申报记录不存在");
            }
            return declaration.getAccidentDetail();
        }
    }
}
