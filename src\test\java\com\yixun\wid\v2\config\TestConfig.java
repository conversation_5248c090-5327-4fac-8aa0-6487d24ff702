package com.yixun.wid.v2.config;

import com.yixun.wid.service.AdministratorService;
import com.yixun.wid.service.UserService;
import com.yixun.wid.v2.utils.AiV2Utils;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.test.context.ActiveProfiles;

import com.mongodb.client.MongoClients;

/**
 * 测试配置类
 */
@TestConfiguration
@ActiveProfiles("test")
public class TestConfig {

    // 模拟外部服务
    @MockBean
    private AdministratorService administratorService;

    @MockBean
    private UserService userService;

    @MockBean
    private AiV2Utils aiV2Utils;

    /**
     * 配置测试用MongoTemplate
     */
    @Bean
    @Primary
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(new SimpleMongoClientDatabaseFactory(MongoClients.create(), "test"));
    }
}
