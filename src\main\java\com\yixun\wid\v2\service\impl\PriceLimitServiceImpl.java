package com.yixun.wid.v2.service.impl;

import com.yixun.wid.v2.entity.PriceLimit;
import com.yixun.wid.v2.service.PriceLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 限价目录服务实现类
 */
@Slf4j
@Service
public class PriceLimitServiceImpl implements PriceLimitService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public PriceLimit findByThreeCatalogueIdAndPriceLimitLevel(Long threeCatalogueId, Integer priceLimitLevel) {
        if (threeCatalogueId == null || priceLimitLevel == null) {
            return null;
        }

        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("threeCatalogueId").is(threeCatalogueId));
            query.addCriteria(Criteria.where("priceLimitLevel").is(String.valueOf(priceLimitLevel)));

            PriceLimit priceLimit = mongoTemplate.findOne(query, PriceLimit.class);
            
            if (priceLimit != null) {
                log.debug("找到限价信息，三目录ID：{}，限价医院等级：{}，上限金额：{}", 
                    threeCatalogueId, priceLimitLevel, priceLimit.getMaxPrice());
            } else {
                log.debug("未找到限价信息，三目录ID：{}，限价医院等级：{}", 
                    threeCatalogueId, priceLimitLevel);
            }
            
            return priceLimit;
        } catch (Exception e) {
            log.error("查询限价信息异常，三目录ID：{}，限价医院等级：{}", 
                threeCatalogueId, priceLimitLevel, e);
            return null;
        }
    }
}
